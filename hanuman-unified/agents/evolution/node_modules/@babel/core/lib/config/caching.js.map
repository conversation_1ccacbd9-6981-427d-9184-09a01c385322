{"version": 3, "names": ["_gensync", "data", "require", "_async", "_util", "synchronize", "gen", "gens<PERSON>", "sync", "gen<PERSON><PERSON>", "makeWeakCache", "handler", "makeCachedFunction", "WeakMap", "makeWeakCacheSync", "makeStrongCache", "Map", "makeStrongCacheSync", "CallCache", "callCacheSync", "callCacheAsync", "futureCache", "cachedFunction", "arg", "asyncContext", "isAsync", "callCache", "cached", "getCachedValueOrWait", "valid", "value", "cache", "CacheConfigurator", "handlerResult", "finishLock", "isIterableIterator", "onFirstPause", "setupAsyncLocks", "updateFunctionCache", "delete", "release", "getCachedValue", "cachedValue", "get", "waitFor", "promise", "config", "Lock", "configured", "forever", "deactivate", "mode", "set", "validator", "push", "constructor", "_active", "_never", "_forever", "_invalidate", "_configured", "_pairs", "_data", "simple", "makeSimpleConfigurator", "Error", "never", "using", "key", "fn", "maybe<PERSON><PERSON>", "isThenable", "then", "invalidate", "pairs", "cacheFn", "val", "assertSimpleType", "cb", "released", "_resolve", "Promise", "resolve"], "sources": ["../../src/config/caching.ts"], "sourcesContent": ["import gensync from \"gensync\";\nimport type { Handler } from \"gensync\";\nimport {\n  maybeAsync,\n  isAsync,\n  onFirstPause,\n  waitFor,\n  isThenable,\n} from \"../gensync-utils/async.ts\";\nimport { isIterableIterator } from \"./util.ts\";\n\nexport type { CacheConfigurator };\n\nexport type SimpleCacheConfigurator = {\n  (forever: boolean): void;\n  <T>(handler: () => T): T;\n\n  forever: () => void;\n  never: () => void;\n  using: <T>(handler: () => T) => T;\n  invalidate: <T>(handler: () => T) => T;\n};\n\nexport type CacheEntry<ResultT, SideChannel> = Array<{\n  value: ResultT;\n  valid: (channel: SideChannel) => Handler<boolean>;\n}>;\n\nconst synchronize = <ArgsT extends unknown[], ResultT>(\n  gen: (...args: ArgsT) => Handler<ResultT>,\n): ((...args: ArgsT) => ResultT) => {\n  return gensync(gen).sync;\n};\n\n// eslint-disable-next-line require-yield\nfunction* genTrue() {\n  return true;\n}\n\nexport function makeWeakCache<ArgT extends object, ResultT, SideChannel>(\n  handler: (\n    arg: ArgT,\n    cache: CacheConfigurator<SideChannel>,\n  ) => Handler<ResultT> | ResultT,\n): (arg: ArgT, data: SideChannel) => Handler<ResultT> {\n  return makeCachedFunction<ArgT, ResultT, SideChannel>(WeakMap, handler);\n}\n\nexport function makeWeakCacheSync<ArgT extends object, ResultT, SideChannel>(\n  handler: (arg: ArgT, cache?: CacheConfigurator<SideChannel>) => ResultT,\n): (arg: ArgT, data?: SideChannel) => ResultT {\n  return synchronize<[ArgT, SideChannel], ResultT>(\n    makeWeakCache<ArgT, ResultT, SideChannel>(handler),\n  );\n}\n\nexport function makeStrongCache<ArgT, ResultT, SideChannel>(\n  handler: (\n    arg: ArgT,\n    cache: CacheConfigurator<SideChannel>,\n  ) => Handler<ResultT> | ResultT,\n): (arg: ArgT, data: SideChannel) => Handler<ResultT> {\n  return makeCachedFunction<ArgT, ResultT, SideChannel>(Map, handler);\n}\n\nexport function makeStrongCacheSync<ArgT, ResultT, SideChannel>(\n  handler: (arg: ArgT, cache?: CacheConfigurator<SideChannel>) => ResultT,\n): (arg: ArgT, data?: SideChannel) => ResultT {\n  return synchronize<[ArgT, SideChannel], ResultT>(\n    makeStrongCache<ArgT, ResultT, SideChannel>(handler),\n  );\n}\n\n/* NOTE: Part of the logic explained in this comment is explained in the\n *       getCachedValueOrWait and setupAsyncLocks functions.\n *\n * > There are only two hard things in Computer Science: cache invalidation and naming things.\n * > -- Phil Karlton\n *\n * I don't know if Phil was also thinking about handling a cache whose invalidation function is\n * defined asynchronously is considered, but it is REALLY hard to do correctly.\n *\n * The implemented logic (only when gensync is run asynchronously) is the following:\n *   1. If there is a valid cache associated to the current \"arg\" parameter,\n *       a. RETURN the cached value\n *   3. If there is a FinishLock associated to the current \"arg\" parameter representing a valid cache,\n *       a. Wait for that lock to be released\n *       b. RETURN the value associated with that lock\n *   5. Start executing the function to be cached\n *       a. If it pauses on a promise, then\n *           i. Let FinishLock be a new lock\n *          ii. Store FinishLock as associated to the current \"arg\" parameter\n *         iii. Wait for the function to finish executing\n *          iv. Release FinishLock\n *           v. Send the function result to anyone waiting on FinishLock\n *   6. Store the result in the cache\n *   7. RETURN the result\n */\nfunction makeCachedFunction<ArgT, ResultT, SideChannel>(\n  CallCache: new <Cached>() => CacheMap<ArgT, Cached, SideChannel>,\n  handler: (\n    arg: ArgT,\n    cache: CacheConfigurator<SideChannel>,\n  ) => Handler<ResultT> | ResultT,\n): (arg: ArgT, data: SideChannel) => Handler<ResultT> {\n  const callCacheSync = new CallCache<ResultT>();\n  const callCacheAsync = new CallCache<ResultT>();\n  const futureCache = new CallCache<Lock<ResultT>>();\n\n  return function* cachedFunction(arg: ArgT, data: SideChannel) {\n    const asyncContext = yield* isAsync();\n    const callCache = asyncContext ? callCacheAsync : callCacheSync;\n\n    const cached = yield* getCachedValueOrWait<ArgT, ResultT, SideChannel>(\n      asyncContext,\n      callCache,\n      futureCache,\n      arg,\n      data,\n    );\n    if (cached.valid) return cached.value;\n\n    const cache = new CacheConfigurator(data);\n\n    const handlerResult: Handler<ResultT> | ResultT = handler(arg, cache);\n\n    let finishLock: Lock<ResultT>;\n    let value: ResultT;\n\n    if (isIterableIterator(handlerResult)) {\n      value = yield* onFirstPause(handlerResult, () => {\n        finishLock = setupAsyncLocks(cache, futureCache, arg);\n      });\n    } else {\n      value = handlerResult;\n    }\n\n    updateFunctionCache(callCache, cache, arg, value);\n\n    if (finishLock) {\n      futureCache.delete(arg);\n      finishLock.release(value);\n    }\n\n    return value;\n  };\n}\n\ntype CacheMap<ArgT, ResultT, SideChannel> =\n  | Map<ArgT, CacheEntry<ResultT, SideChannel>>\n  // @ts-expect-error todo(flow->ts): add `extends object` constraint to ArgT\n  | WeakMap<ArgT, CacheEntry<ResultT, SideChannel>>;\n\nfunction* getCachedValue<ArgT, ResultT, SideChannel>(\n  cache: CacheMap<ArgT, ResultT, SideChannel>,\n  arg: ArgT,\n  data: SideChannel,\n): Handler<{ valid: true; value: ResultT } | { valid: false; value: null }> {\n  const cachedValue: CacheEntry<ResultT, SideChannel> | void = cache.get(arg);\n\n  if (cachedValue) {\n    for (const { value, valid } of cachedValue) {\n      if (yield* valid(data)) return { valid: true, value };\n    }\n  }\n\n  return { valid: false, value: null };\n}\n\nfunction* getCachedValueOrWait<ArgT, ResultT, SideChannel>(\n  asyncContext: boolean,\n  callCache: CacheMap<ArgT, ResultT, SideChannel>,\n  futureCache: CacheMap<ArgT, Lock<ResultT>, SideChannel>,\n  arg: ArgT,\n  data: SideChannel,\n): Handler<{ valid: true; value: ResultT } | { valid: false; value: null }> {\n  const cached = yield* getCachedValue(callCache, arg, data);\n  if (cached.valid) {\n    return cached;\n  }\n\n  if (asyncContext) {\n    const cached = yield* getCachedValue(futureCache, arg, data);\n    if (cached.valid) {\n      const value = yield* waitFor<ResultT>(cached.value.promise);\n      return { valid: true, value };\n    }\n  }\n\n  return { valid: false, value: null };\n}\n\nfunction setupAsyncLocks<ArgT, ResultT, SideChannel>(\n  config: CacheConfigurator<SideChannel>,\n  futureCache: CacheMap<ArgT, Lock<ResultT>, SideChannel>,\n  arg: ArgT,\n): Lock<ResultT> {\n  const finishLock = new Lock<ResultT>();\n\n  updateFunctionCache(futureCache, config, arg, finishLock);\n\n  return finishLock;\n}\n\nfunction updateFunctionCache<\n  ArgT,\n  ResultT,\n  SideChannel,\n  Cache extends CacheMap<ArgT, ResultT, SideChannel>,\n>(\n  cache: Cache,\n  config: CacheConfigurator<SideChannel>,\n  arg: ArgT,\n  value: ResultT,\n) {\n  if (!config.configured()) config.forever();\n\n  let cachedValue: CacheEntry<ResultT, SideChannel> | void = cache.get(arg);\n\n  config.deactivate();\n\n  switch (config.mode()) {\n    case \"forever\":\n      cachedValue = [{ value, valid: genTrue }];\n      cache.set(arg, cachedValue);\n      break;\n    case \"invalidate\":\n      cachedValue = [{ value, valid: config.validator() }];\n      cache.set(arg, cachedValue);\n      break;\n    case \"valid\":\n      if (cachedValue) {\n        cachedValue.push({ value, valid: config.validator() });\n      } else {\n        cachedValue = [{ value, valid: config.validator() }];\n        cache.set(arg, cachedValue);\n      }\n  }\n}\n\nclass CacheConfigurator<SideChannel = void> {\n  _active: boolean = true;\n  _never: boolean = false;\n  _forever: boolean = false;\n  _invalidate: boolean = false;\n\n  _configured: boolean = false;\n\n  _pairs: Array<\n    [cachedValue: unknown, handler: (data: SideChannel) => Handler<unknown>]\n  > = [];\n\n  _data: SideChannel;\n\n  constructor(data: SideChannel) {\n    this._data = data;\n  }\n\n  simple() {\n    return makeSimpleConfigurator(this);\n  }\n\n  mode() {\n    if (this._never) return \"never\";\n    if (this._forever) return \"forever\";\n    if (this._invalidate) return \"invalidate\";\n    return \"valid\";\n  }\n\n  forever() {\n    if (!this._active) {\n      throw new Error(\"Cannot change caching after evaluation has completed.\");\n    }\n    if (this._never) {\n      throw new Error(\"Caching has already been configured with .never()\");\n    }\n    this._forever = true;\n    this._configured = true;\n  }\n\n  never() {\n    if (!this._active) {\n      throw new Error(\"Cannot change caching after evaluation has completed.\");\n    }\n    if (this._forever) {\n      throw new Error(\"Caching has already been configured with .forever()\");\n    }\n    this._never = true;\n    this._configured = true;\n  }\n\n  using<T>(handler: (data: SideChannel) => T): T {\n    if (!this._active) {\n      throw new Error(\"Cannot change caching after evaluation has completed.\");\n    }\n    if (this._never || this._forever) {\n      throw new Error(\n        \"Caching has already been configured with .never or .forever()\",\n      );\n    }\n    this._configured = true;\n\n    const key = handler(this._data);\n\n    const fn = maybeAsync(\n      handler,\n      `You appear to be using an async cache handler, but Babel has been called synchronously`,\n    );\n\n    if (isThenable(key)) {\n      // @ts-expect-error todo(flow->ts): improve function return type annotation\n      return key.then((key: unknown) => {\n        this._pairs.push([key, fn]);\n        return key;\n      });\n    }\n\n    this._pairs.push([key, fn]);\n    return key;\n  }\n\n  invalidate<T>(handler: (data: SideChannel) => T): T {\n    this._invalidate = true;\n    return this.using(handler);\n  }\n\n  validator(): (data: SideChannel) => Handler<boolean> {\n    const pairs = this._pairs;\n    return function* (data: SideChannel) {\n      for (const [key, fn] of pairs) {\n        if (key !== (yield* fn(data))) return false;\n      }\n      return true;\n    };\n  }\n\n  deactivate() {\n    this._active = false;\n  }\n\n  configured() {\n    return this._configured;\n  }\n}\n\nfunction makeSimpleConfigurator(\n  cache: CacheConfigurator<any>,\n): SimpleCacheConfigurator {\n  function cacheFn(val: any) {\n    if (typeof val === \"boolean\") {\n      if (val) cache.forever();\n      else cache.never();\n      return;\n    }\n\n    return cache.using(() => assertSimpleType(val()));\n  }\n  cacheFn.forever = () => cache.forever();\n  cacheFn.never = () => cache.never();\n  cacheFn.using = (cb: () => SimpleType) =>\n    cache.using(() => assertSimpleType(cb()));\n  cacheFn.invalidate = (cb: () => SimpleType) =>\n    cache.invalidate(() => assertSimpleType(cb()));\n\n  return cacheFn as any;\n}\n\n// Types are limited here so that in the future these values can be used\n// as part of Babel's caching logic.\nexport type SimpleType =\n  | string\n  | boolean\n  | number\n  | null\n  | void\n  | Promise<SimpleType>;\nexport function assertSimpleType(value: unknown): SimpleType {\n  if (isThenable(value)) {\n    throw new Error(\n      `You appear to be using an async cache handler, ` +\n        `which your current version of Babel does not support. ` +\n        `We may add support for this in the future, ` +\n        `but if you're on the most recent version of @babel/core and still ` +\n        `seeing this error, then you'll need to synchronously handle your caching logic.`,\n    );\n  }\n\n  if (\n    value != null &&\n    typeof value !== \"string\" &&\n    typeof value !== \"boolean\" &&\n    typeof value !== \"number\"\n  ) {\n    throw new Error(\n      \"Cache keys must be either string, boolean, number, null, or undefined.\",\n    );\n  }\n  // @ts-expect-error Type 'unknown' is not assignable to type 'SimpleType'. This can be removed\n  // when strictNullCheck is enabled\n  return value;\n}\n\nclass Lock<T> {\n  released: boolean = false;\n  promise: Promise<T>;\n  _resolve: (value: T) => void;\n\n  constructor() {\n    this.promise = new Promise(resolve => {\n      this._resolve = resolve;\n    });\n  }\n\n  release(value: T) {\n    this.released = true;\n    this._resolve(value);\n  }\n}\n"], "mappings": ";;;;;;;;;;AAAA,SAAAA,SAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,QAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,IAAAE,MAAA,GAAAD,OAAA;AAOA,IAAAE,KAAA,GAAAF,OAAA;AAmBA,MAAMG,WAAW,GACfC,GAAyC,IACP;EAClC,OAAOC,SAAMA,CAAC,CAACD,GAAG,CAAC,CAACE,IAAI;AAC1B,CAAC;AAGD,UAAUC,OAAOA,CAAA,EAAG;EAClB,OAAO,IAAI;AACb;AAEO,SAASC,aAAaA,CAC3BC,OAG+B,EACqB;EACpD,OAAOC,kBAAkB,CAA6BC,OAAO,EAAEF,OAAO,CAAC;AACzE;AAEO,SAASG,iBAAiBA,CAC/BH,OAAuE,EAC3B;EAC5C,OAAON,WAAW,CAChBK,aAAa,CAA6BC,OAAO,CACnD,CAAC;AACH;AAEO,SAASI,eAAeA,CAC7BJ,OAG+B,EACqB;EACpD,OAAOC,kBAAkB,CAA6BI,GAAG,EAAEL,OAAO,CAAC;AACrE;AAEO,SAASM,mBAAmBA,CACjCN,OAAuE,EAC3B;EAC5C,OAAON,WAAW,CAChBU,eAAe,CAA6BJ,OAAO,CACrD,CAAC;AACH;AA2BA,SAASC,kBAAkBA,CACzBM,SAAgE,EAChEP,OAG+B,EACqB;EACpD,MAAMQ,aAAa,GAAG,IAAID,SAAS,CAAU,CAAC;EAC9C,MAAME,cAAc,GAAG,IAAIF,SAAS,CAAU,CAAC;EAC/C,MAAMG,WAAW,GAAG,IAAIH,SAAS,CAAgB,CAAC;EAElD,OAAO,UAAUI,cAAcA,CAACC,GAAS,EAAEtB,IAAiB,EAAE;IAC5D,MAAMuB,YAAY,GAAG,OAAO,IAAAC,cAAO,EAAC,CAAC;IACrC,MAAMC,SAAS,GAAGF,YAAY,GAAGJ,cAAc,GAAGD,aAAa;IAE/D,MAAMQ,MAAM,GAAG,OAAOC,oBAAoB,CACxCJ,YAAY,EACZE,SAAS,EACTL,WAAW,EACXE,GAAG,EACHtB,IACF,CAAC;IACD,IAAI0B,MAAM,CAACE,KAAK,EAAE,OAAOF,MAAM,CAACG,KAAK;IAErC,MAAMC,KAAK,GAAG,IAAIC,iBAAiB,CAAC/B,IAAI,CAAC;IAEzC,MAAMgC,aAAyC,GAAGtB,OAAO,CAACY,GAAG,EAAEQ,KAAK,CAAC;IAErE,IAAIG,UAAyB;IAC7B,IAAIJ,KAAc;IAElB,IAAI,IAAAK,wBAAkB,EAACF,aAAa,CAAC,EAAE;MACrCH,KAAK,GAAG,OAAO,IAAAM,mBAAY,EAACH,aAAa,EAAE,MAAM;QAC/CC,UAAU,GAAGG,eAAe,CAACN,KAAK,EAAEV,WAAW,EAAEE,GAAG,CAAC;MACvD,CAAC,CAAC;IACJ,CAAC,MAAM;MACLO,KAAK,GAAGG,aAAa;IACvB;IAEAK,mBAAmB,CAACZ,SAAS,EAAEK,KAAK,EAAER,GAAG,EAAEO,KAAK,CAAC;IAEjD,IAAII,UAAU,EAAE;MACdb,WAAW,CAACkB,MAAM,CAAChB,GAAG,CAAC;MACvBW,UAAU,CAACM,OAAO,CAACV,KAAK,CAAC;IAC3B;IAEA,OAAOA,KAAK;EACd,CAAC;AACH;AAOA,UAAUW,cAAcA,CACtBV,KAA2C,EAC3CR,GAAS,EACTtB,IAAiB,EACyD;EAC1E,MAAMyC,WAAoD,GAAGX,KAAK,CAACY,GAAG,CAACpB,GAAG,CAAC;EAE3E,IAAImB,WAAW,EAAE;IACf,KAAK,MAAM;MAAEZ,KAAK;MAAED;IAAM,CAAC,IAAIa,WAAW,EAAE;MAC1C,IAAI,OAAOb,KAAK,CAAC5B,IAAI,CAAC,EAAE,OAAO;QAAE4B,KAAK,EAAE,IAAI;QAAEC;MAAM,CAAC;IACvD;EACF;EAEA,OAAO;IAAED,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAK,CAAC;AACtC;AAEA,UAAUF,oBAAoBA,CAC5BJ,YAAqB,EACrBE,SAA+C,EAC/CL,WAAuD,EACvDE,GAAS,EACTtB,IAAiB,EACyD;EAC1E,MAAM0B,MAAM,GAAG,OAAOc,cAAc,CAACf,SAAS,EAAEH,GAAG,EAAEtB,IAAI,CAAC;EAC1D,IAAI0B,MAAM,CAACE,KAAK,EAAE;IAChB,OAAOF,MAAM;EACf;EAEA,IAAIH,YAAY,EAAE;IAChB,MAAMG,MAAM,GAAG,OAAOc,cAAc,CAACpB,WAAW,EAAEE,GAAG,EAAEtB,IAAI,CAAC;IAC5D,IAAI0B,MAAM,CAACE,KAAK,EAAE;MAChB,MAAMC,KAAK,GAAG,OAAO,IAAAc,cAAO,EAAUjB,MAAM,CAACG,KAAK,CAACe,OAAO,CAAC;MAC3D,OAAO;QAAEhB,KAAK,EAAE,IAAI;QAAEC;MAAM,CAAC;IAC/B;EACF;EAEA,OAAO;IAAED,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAK,CAAC;AACtC;AAEA,SAASO,eAAeA,CACtBS,MAAsC,EACtCzB,WAAuD,EACvDE,GAAS,EACM;EACf,MAAMW,UAAU,GAAG,IAAIa,IAAI,CAAU,CAAC;EAEtCT,mBAAmB,CAACjB,WAAW,EAAEyB,MAAM,EAAEvB,GAAG,EAAEW,UAAU,CAAC;EAEzD,OAAOA,UAAU;AACnB;AAEA,SAASI,mBAAmBA,CAM1BP,KAAY,EACZe,MAAsC,EACtCvB,GAAS,EACTO,KAAc,EACd;EACA,IAAI,CAACgB,MAAM,CAACE,UAAU,CAAC,CAAC,EAAEF,MAAM,CAACG,OAAO,CAAC,CAAC;EAE1C,IAAIP,WAAoD,GAAGX,KAAK,CAACY,GAAG,CAACpB,GAAG,CAAC;EAEzEuB,MAAM,CAACI,UAAU,CAAC,CAAC;EAEnB,QAAQJ,MAAM,CAACK,IAAI,CAAC,CAAC;IACnB,KAAK,SAAS;MACZT,WAAW,GAAG,CAAC;QAAEZ,KAAK;QAAED,KAAK,EAAEpB;MAAQ,CAAC,CAAC;MACzCsB,KAAK,CAACqB,GAAG,CAAC7B,GAAG,EAAEmB,WAAW,CAAC;MAC3B;IACF,KAAK,YAAY;MACfA,WAAW,GAAG,CAAC;QAAEZ,KAAK;QAAED,KAAK,EAAEiB,MAAM,CAACO,SAAS,CAAC;MAAE,CAAC,CAAC;MACpDtB,KAAK,CAACqB,GAAG,CAAC7B,GAAG,EAAEmB,WAAW,CAAC;MAC3B;IACF,KAAK,OAAO;MACV,IAAIA,WAAW,EAAE;QACfA,WAAW,CAACY,IAAI,CAAC;UAAExB,KAAK;UAAED,KAAK,EAAEiB,MAAM,CAACO,SAAS,CAAC;QAAE,CAAC,CAAC;MACxD,CAAC,MAAM;QACLX,WAAW,GAAG,CAAC;UAAEZ,KAAK;UAAED,KAAK,EAAEiB,MAAM,CAACO,SAAS,CAAC;QAAE,CAAC,CAAC;QACpDtB,KAAK,CAACqB,GAAG,CAAC7B,GAAG,EAAEmB,WAAW,CAAC;MAC7B;EACJ;AACF;AAEA,MAAMV,iBAAiB,CAAqB;EAc1CuB,WAAWA,CAACtD,IAAiB,EAAE;IAAA,KAb/BuD,OAAO,GAAY,IAAI;IAAA,KACvBC,MAAM,GAAY,KAAK;IAAA,KACvBC,QAAQ,GAAY,KAAK;IAAA,KACzBC,WAAW,GAAY,KAAK;IAAA,KAE5BC,WAAW,GAAY,KAAK;IAAA,KAE5BC,MAAM,GAEF,EAAE;IAAA,KAENC,KAAK;IAGH,IAAI,CAACA,KAAK,GAAG7D,IAAI;EACnB;EAEA8D,MAAMA,CAAA,EAAG;IACP,OAAOC,sBAAsB,CAAC,IAAI,CAAC;EACrC;EAEAb,IAAIA,CAAA,EAAG;IACL,IAAI,IAAI,CAACM,MAAM,EAAE,OAAO,OAAO;IAC/B,IAAI,IAAI,CAACC,QAAQ,EAAE,OAAO,SAAS;IACnC,IAAI,IAAI,CAACC,WAAW,EAAE,OAAO,YAAY;IACzC,OAAO,OAAO;EAChB;EAEAV,OAAOA,CAAA,EAAG;IACR,IAAI,CAAC,IAAI,CAACO,OAAO,EAAE;MACjB,MAAM,IAAIS,KAAK,CAAC,uDAAuD,CAAC;IAC1E;IACA,IAAI,IAAI,CAACR,MAAM,EAAE;MACf,MAAM,IAAIQ,KAAK,CAAC,mDAAmD,CAAC;IACtE;IACA,IAAI,CAACP,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACE,WAAW,GAAG,IAAI;EACzB;EAEAM,KAAKA,CAAA,EAAG;IACN,IAAI,CAAC,IAAI,CAACV,OAAO,EAAE;MACjB,MAAM,IAAIS,KAAK,CAAC,uDAAuD,CAAC;IAC1E;IACA,IAAI,IAAI,CAACP,QAAQ,EAAE;MACjB,MAAM,IAAIO,KAAK,CAAC,qDAAqD,CAAC;IACxE;IACA,IAAI,CAACR,MAAM,GAAG,IAAI;IAClB,IAAI,CAACG,WAAW,GAAG,IAAI;EACzB;EAEAO,KAAKA,CAAIxD,OAAiC,EAAK;IAC7C,IAAI,CAAC,IAAI,CAAC6C,OAAO,EAAE;MACjB,MAAM,IAAIS,KAAK,CAAC,uDAAuD,CAAC;IAC1E;IACA,IAAI,IAAI,CAACR,MAAM,IAAI,IAAI,CAACC,QAAQ,EAAE;MAChC,MAAM,IAAIO,KAAK,CACb,+DACF,CAAC;IACH;IACA,IAAI,CAACL,WAAW,GAAG,IAAI;IAEvB,MAAMQ,GAAG,GAAGzD,OAAO,CAAC,IAAI,CAACmD,KAAK,CAAC;IAE/B,MAAMO,EAAE,GAAG,IAAAC,iBAAU,EACnB3D,OAAO,EACP,wFACF,CAAC;IAED,IAAI,IAAA4D,iBAAU,EAACH,GAAG,CAAC,EAAE;MAEnB,OAAOA,GAAG,CAACI,IAAI,CAAEJ,GAAY,IAAK;QAChC,IAAI,CAACP,MAAM,CAACP,IAAI,CAAC,CAACc,GAAG,EAAEC,EAAE,CAAC,CAAC;QAC3B,OAAOD,GAAG;MACZ,CAAC,CAAC;IACJ;IAEA,IAAI,CAACP,MAAM,CAACP,IAAI,CAAC,CAACc,GAAG,EAAEC,EAAE,CAAC,CAAC;IAC3B,OAAOD,GAAG;EACZ;EAEAK,UAAUA,CAAI9D,OAAiC,EAAK;IAClD,IAAI,CAACgD,WAAW,GAAG,IAAI;IACvB,OAAO,IAAI,CAACQ,KAAK,CAACxD,OAAO,CAAC;EAC5B;EAEA0C,SAASA,CAAA,EAA4C;IACnD,MAAMqB,KAAK,GAAG,IAAI,CAACb,MAAM;IACzB,OAAO,WAAW5D,IAAiB,EAAE;MACnC,KAAK,MAAM,CAACmE,GAAG,EAAEC,EAAE,CAAC,IAAIK,KAAK,EAAE;QAC7B,IAAIN,GAAG,MAAM,OAAOC,EAAE,CAACpE,IAAI,CAAC,CAAC,EAAE,OAAO,KAAK;MAC7C;MACA,OAAO,IAAI;IACb,CAAC;EACH;EAEAiD,UAAUA,CAAA,EAAG;IACX,IAAI,CAACM,OAAO,GAAG,KAAK;EACtB;EAEAR,UAAUA,CAAA,EAAG;IACX,OAAO,IAAI,CAACY,WAAW;EACzB;AACF;AAEA,SAASI,sBAAsBA,CAC7BjC,KAA6B,EACJ;EACzB,SAAS4C,OAAOA,CAACC,GAAQ,EAAE;IACzB,IAAI,OAAOA,GAAG,KAAK,SAAS,EAAE;MAC5B,IAAIA,GAAG,EAAE7C,KAAK,CAACkB,OAAO,CAAC,CAAC,CAAC,KACpBlB,KAAK,CAACmC,KAAK,CAAC,CAAC;MAClB;IACF;IAEA,OAAOnC,KAAK,CAACoC,KAAK,CAAC,MAAMU,gBAAgB,CAACD,GAAG,CAAC,CAAC,CAAC,CAAC;EACnD;EACAD,OAAO,CAAC1B,OAAO,GAAG,MAAMlB,KAAK,CAACkB,OAAO,CAAC,CAAC;EACvC0B,OAAO,CAACT,KAAK,GAAG,MAAMnC,KAAK,CAACmC,KAAK,CAAC,CAAC;EACnCS,OAAO,CAACR,KAAK,GAAIW,EAAoB,IACnC/C,KAAK,CAACoC,KAAK,CAAC,MAAMU,gBAAgB,CAACC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC3CH,OAAO,CAACF,UAAU,GAAIK,EAAoB,IACxC/C,KAAK,CAAC0C,UAAU,CAAC,MAAMI,gBAAgB,CAACC,EAAE,CAAC,CAAC,CAAC,CAAC;EAEhD,OAAOH,OAAO;AAChB;AAWO,SAASE,gBAAgBA,CAAC/C,KAAc,EAAc;EAC3D,IAAI,IAAAyC,iBAAU,EAACzC,KAAK,CAAC,EAAE;IACrB,MAAM,IAAImC,KAAK,CACb,iDAAiD,GAC/C,wDAAwD,GACxD,6CAA6C,GAC7C,oEAAoE,GACpE,iFACJ,CAAC;EACH;EAEA,IACEnC,KAAK,IAAI,IAAI,IACb,OAAOA,KAAK,KAAK,QAAQ,IACzB,OAAOA,KAAK,KAAK,SAAS,IAC1B,OAAOA,KAAK,KAAK,QAAQ,EACzB;IACA,MAAM,IAAImC,KAAK,CACb,wEACF,CAAC;EACH;EAGA,OAAOnC,KAAK;AACd;AAEA,MAAMiB,IAAI,CAAI;EAKZQ,WAAWA,CAAA,EAAG;IAAA,KAJdwB,QAAQ,GAAY,KAAK;IAAA,KACzBlC,OAAO;IAAA,KACPmC,QAAQ;IAGN,IAAI,CAACnC,OAAO,GAAG,IAAIoC,OAAO,CAACC,OAAO,IAAI;MACpC,IAAI,CAACF,QAAQ,GAAGE,OAAO;IACzB,CAAC,CAAC;EACJ;EAEA1C,OAAOA,CAACV,KAAQ,EAAE;IAChB,IAAI,CAACiD,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,QAAQ,CAAClD,KAAK,CAAC;EACtB;AACF;AAAC", "ignoreList": []}