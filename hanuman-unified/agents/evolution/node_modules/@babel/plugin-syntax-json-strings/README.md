# @babel/plugin-syntax-json-strings

> Allow parsing of the U+2028 LINE SEPARATOR and U+2029 PARAGRAPH SEPARATOR in JS strings

See our website [@babel/plugin-syntax-json-strings](https://babeljs.io/docs/en/next/babel-plugin-syntax-json-strings.html) for more information.

## Install

Using npm:

```sh
npm install --save-dev @babel/plugin-syntax-json-strings
```

or using yarn:

```sh
yarn add @babel/plugin-syntax-json-strings --dev
```
