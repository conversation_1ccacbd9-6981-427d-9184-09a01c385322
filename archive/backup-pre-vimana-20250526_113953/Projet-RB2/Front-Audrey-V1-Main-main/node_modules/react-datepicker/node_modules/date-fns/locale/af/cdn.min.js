(()=>{var A;function C(B){return C=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(H){return typeof H}:function(H){return H&&typeof Symbol=="function"&&H.constructor===Symbol&&H!==Symbol.prototype?"symbol":typeof H},C(B)}function x(B,H){var G=Object.keys(B);if(Object.getOwnPropertySymbols){var J=Object.getOwnPropertySymbols(B);H&&(J=J.filter(function(X){return Object.getOwnPropertyDescriptor(B,X).enumerable})),G.push.apply(G,J)}return G}function Q(B){for(var H=1;H<arguments.length;H++){var G=arguments[H]!=null?arguments[H]:{};H%2?x(Object(G),!0).forEach(function(J){N(B,J,G[J])}):Object.getOwnPropertyDescriptors?Object.defineProperties(B,Object.getOwnPropertyDescriptors(G)):x(Object(G)).forEach(function(J){Object.defineProperty(B,J,Object.getOwnPropertyDescriptor(G,J))})}return B}function N(B,H,G){if(H=z(H),H in B)Object.defineProperty(B,H,{value:G,enumerable:!0,configurable:!0,writable:!0});else B[H]=G;return B}function z(B){var H=W(B,"string");return C(H)=="symbol"?H:String(H)}function W(B,H){if(C(B)!="object"||!B)return B;var G=B[Symbol.toPrimitive];if(G!==void 0){var J=G.call(B,H||"default");if(C(J)!="object")return J;throw new TypeError("@@toPrimitive must return a primitive value.")}return(H==="string"?String:Number)(B)}var E=Object.defineProperty,HB=function B(H,G){for(var J in G)E(H,J,{get:G[J],enumerable:!0,configurable:!0,set:function X(Y){return G[J]=function(){return Y}}})},D={lessThanXSeconds:{one:"minder as 'n sekonde",other:"minder as {{count}} sekondes"},xSeconds:{one:"1 sekonde",other:"{{count}} sekondes"},halfAMinute:"'n halwe minuut",lessThanXMinutes:{one:"minder as 'n minuut",other:"minder as {{count}} minute"},xMinutes:{one:"'n minuut",other:"{{count}} minute"},aboutXHours:{one:"ongeveer 1 uur",other:"ongeveer {{count}} ure"},xHours:{one:"1 uur",other:"{{count}} ure"},xDays:{one:"1 dag",other:"{{count}} dae"},aboutXWeeks:{one:"ongeveer 1 week",other:"ongeveer {{count}} weke"},xWeeks:{one:"1 week",other:"{{count}} weke"},aboutXMonths:{one:"ongeveer 1 maand",other:"ongeveer {{count}} maande"},xMonths:{one:"1 maand",other:"{{count}} maande"},aboutXYears:{one:"ongeveer 1 jaar",other:"ongeveer {{count}} jaar"},xYears:{one:"1 jaar",other:"{{count}} jaar"},overXYears:{one:"meer as 1 jaar",other:"meer as {{count}} jaar"},almostXYears:{one:"byna 1 jaar",other:"byna {{count}} jaar"}},S=function B(H,G,J){var X,Y=D[H];if(typeof Y==="string")X=Y;else if(G===1)X=Y.one;else X=Y.other.replace("{{count}}",String(G));if(J!==null&&J!==void 0&&J.addSuffix)if(J.comparison&&J.comparison>0)return"oor "+X;else return X+" gelede";return X};function $(B){return function(){var H=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},G=H.width?String(H.width):B.defaultWidth,J=B.formats[G]||B.formats[B.defaultWidth];return J}}var M={full:"EEEE, d MMMM yyyy",long:"d MMMM yyyy",medium:"d MMM yyyy",short:"yyyy/MM/dd"},R={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},L={full:"{{date}} 'om' {{time}}",long:"{{date}} 'om' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},V={date:$({formats:M,defaultWidth:"full"}),time:$({formats:R,defaultWidth:"full"}),dateTime:$({formats:L,defaultWidth:"full"})},j={lastWeek:"'verlede' eeee 'om' p",yesterday:"'gister om' p",today:"'vandag om' p",tomorrow:"'m\xF4re om' p",nextWeek:"eeee 'om' p",other:"P"},w=function B(H,G,J,X){return j[H]};function I(B){return function(H,G){var J=G!==null&&G!==void 0&&G.context?String(G.context):"standalone",X;if(J==="formatting"&&B.formattingValues){var Y=B.defaultFormattingWidth||B.defaultWidth,Z=G!==null&&G!==void 0&&G.width?String(G.width):Y;X=B.formattingValues[Z]||B.formattingValues[Y]}else{var T=B.defaultWidth,q=G!==null&&G!==void 0&&G.width?String(G.width):B.defaultWidth;X=B.values[q]||B.values[T]}var U=B.argumentCallback?B.argumentCallback(H):H;return X[U]}}var _={narrow:["vC","nC"],abbreviated:["vC","nC"],wide:["voor Christus","na Christus"]},f={narrow:["1","2","3","4"],abbreviated:["K1","K2","K3","K4"],wide:["1ste kwartaal","2de kwartaal","3de kwartaal","4de kwartaal"]},v={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mrt","Apr","Mei","Jun","Jul","Aug","Sep","Okt","Nov","Des"],wide:["Januarie","Februarie","Maart","April","Mei","Junie","Julie","Augustus","September","Oktober","November","Desember"]},F={narrow:["S","M","D","W","D","V","S"],short:["So","Ma","Di","Wo","Do","Vr","Sa"],abbreviated:["Son","Maa","Din","Woe","Don","Vry","Sat"],wide:["Sondag","Maandag","Dinsdag","Woensdag","Donderdag","Vrydag","Saterdag"]},P={narrow:{am:"vm",pm:"nm",midnight:"middernag",noon:"middaguur",morning:"oggend",afternoon:"middag",evening:"laat middag",night:"aand"},abbreviated:{am:"vm",pm:"nm",midnight:"middernag",noon:"middaguur",morning:"oggend",afternoon:"middag",evening:"laat middag",night:"aand"},wide:{am:"vm",pm:"nm",midnight:"middernag",noon:"middaguur",morning:"oggend",afternoon:"middag",evening:"laat middag",night:"aand"}},k={narrow:{am:"vm",pm:"nm",midnight:"middernag",noon:"uur die middag",morning:"uur die oggend",afternoon:"uur die middag",evening:"uur die aand",night:"uur die aand"},abbreviated:{am:"vm",pm:"nm",midnight:"middernag",noon:"uur die middag",morning:"uur die oggend",afternoon:"uur die middag",evening:"uur die aand",night:"uur die aand"},wide:{am:"vm",pm:"nm",midnight:"middernag",noon:"uur die middag",morning:"uur die oggend",afternoon:"uur die middag",evening:"uur die aand",night:"uur die aand"}},h=function B(H){var G=Number(H),J=G%100;if(J<20)switch(J){case 1:case 8:return G+"ste";default:return G+"de"}return G+"ste"},b={ordinalNumber:h,era:I({values:_,defaultWidth:"wide"}),quarter:I({values:f,defaultWidth:"wide",argumentCallback:function B(H){return H-1}}),month:I({values:v,defaultWidth:"wide"}),day:I({values:F,defaultWidth:"wide"}),dayPeriod:I({values:P,defaultWidth:"wide",formattingValues:k,defaultFormattingWidth:"wide"})};function O(B){return function(H){var G=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},J=G.width,X=J&&B.matchPatterns[J]||B.matchPatterns[B.defaultMatchWidth],Y=H.match(X);if(!Y)return null;var Z=Y[0],T=J&&B.parsePatterns[J]||B.parsePatterns[B.defaultParseWidth],q=Array.isArray(T)?c(T,function(K){return K.test(Z)}):m(T,function(K){return K.test(Z)}),U;U=B.valueCallback?B.valueCallback(q):q,U=G.valueCallback?G.valueCallback(U):U;var GB=H.slice(Z.length);return{value:U,rest:GB}}}function m(B,H){for(var G in B)if(Object.prototype.hasOwnProperty.call(B,G)&&H(B[G]))return G;return}function c(B,H){for(var G=0;G<B.length;G++)if(H(B[G]))return G;return}function y(B){return function(H){var G=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},J=H.match(B.matchPattern);if(!J)return null;var X=J[0],Y=H.match(B.parsePattern);if(!Y)return null;var Z=B.valueCallback?B.valueCallback(Y[0]):Y[0];Z=G.valueCallback?G.valueCallback(Z):Z;var T=H.slice(X.length);return{value:Z,rest:T}}}var p=/^(\d+)(ste|de)?/i,d=/\d+/i,g={narrow:/^([vn]\.? ?C\.?)/,abbreviated:/^([vn]\. ?C\.?)/,wide:/^((voor|na) Christus)/},u={any:[/^v/,/^n/]},l={narrow:/^[1234]/i,abbreviated:/^K[1234]/i,wide:/^[1234](st|d)e kwartaal/i},i={any:[/1/i,/2/i,/3/i,/4/i]},n={narrow:/^[jfmasond]/i,abbreviated:/^(Jan|Feb|Mrt|Apr|Mei|Jun|Jul|Aug|Sep|Okt|Nov|Dec)\.?/i,wide:/^(Januarie|Februarie|Maart|April|Mei|Junie|Julie|Augustus|September|Oktober|November|Desember)/i},s={narrow:[/^J/i,/^F/i,/^M/i,/^A/i,/^M/i,/^J/i,/^J/i,/^A/i,/^S/i,/^O/i,/^N/i,/^D/i],any:[/^Jan/i,/^Feb/i,/^Mrt/i,/^Apr/i,/^Mei/i,/^Jun/i,/^Jul/i,/^Aug/i,/^Sep/i,/^Okt/i,/^Nov/i,/^Dec/i]},o={narrow:/^[smdwv]/i,short:/^(So|Ma|Di|Wo|Do|Vr|Sa)/i,abbreviated:/^(Son|Maa|Din|Woe|Don|Vry|Sat)/i,wide:/^(Sondag|Maandag|Dinsdag|Woensdag|Donderdag|Vrydag|Saterdag)/i},r={narrow:[/^S/i,/^M/i,/^D/i,/^W/i,/^D/i,/^V/i,/^S/i],any:[/^So/i,/^Ma/i,/^Di/i,/^Wo/i,/^Do/i,/^Vr/i,/^Sa/i]},a={any:/^(vm|nm|middernag|(?:uur )?die (oggend|middag|aand))/i},e={any:{am:/^vm/i,pm:/^nm/i,midnight:/^middernag/i,noon:/^middaguur/i,morning:/oggend/i,afternoon:/middag/i,evening:/laat middag/i,night:/aand/i}},t={ordinalNumber:y({matchPattern:p,parsePattern:d,valueCallback:function B(H){return parseInt(H,10)}}),era:O({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"any"}),quarter:O({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any",valueCallback:function B(H){return H+1}}),month:O({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),day:O({matchPatterns:o,defaultMatchWidth:"wide",parsePatterns:r,defaultParseWidth:"any"}),dayPeriod:O({matchPatterns:a,defaultMatchWidth:"any",parsePatterns:e,defaultParseWidth:"any"})},BB={code:"af",formatDistance:S,formatLong:V,formatRelative:w,localize:b,match:t,options:{weekStartsOn:0,firstWeekContainsDate:1}};window.dateFns=Q(Q({},window.dateFns),{},{locale:Q(Q({},(A=window.dateFns)===null||A===void 0?void 0:A.locale),{},{af:BB})})})();

//# debugId=15A69952D302521964756E2164756E21
