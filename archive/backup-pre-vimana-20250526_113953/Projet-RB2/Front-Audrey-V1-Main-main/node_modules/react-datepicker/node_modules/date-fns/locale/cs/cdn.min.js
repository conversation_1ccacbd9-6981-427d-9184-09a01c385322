(()=>{var A;function I(B){return I=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(C){return typeof C}:function(C){return C&&typeof Symbol=="function"&&C.constructor===Symbol&&C!==Symbol.prototype?"symbol":typeof C},I(B)}function N(B,C){var G=Object.keys(B);if(Object.getOwnPropertySymbols){var H=Object.getOwnPropertySymbols(B);C&&(H=H.filter(function(J){return Object.getOwnPropertyDescriptor(B,J).enumerable})),G.push.apply(G,H)}return G}function q(B){for(var C=1;C<arguments.length;C++){var G=arguments[C]!=null?arguments[C]:{};C%2?N(Object(G),!0).forEach(function(H){z(B,H,G[H])}):Object.getOwnPropertyDescriptors?Object.defineProperties(B,Object.getOwnPropertyDescriptors(G)):N(Object(G)).forEach(function(H){Object.defineProperty(B,H,Object.getOwnPropertyDescriptor(G,H))})}return B}function z(B,C,G){if(C=W(C),C in B)Object.defineProperty(B,C,{value:G,enumerable:!0,configurable:!0,writable:!0});else B[C]=G;return B}function W(B){var C=E(B,"string");return I(C)=="symbol"?C:String(C)}function E(B,C){if(I(B)!="object"||!B)return B;var G=B[Symbol.toPrimitive];if(G!==void 0){var H=G.call(B,C||"default");if(I(H)!="object")return H;throw new TypeError("@@toPrimitive must return a primitive value.")}return(C==="string"?String:Number)(B)}var S=Object.defineProperty,JB=function B(C,G){for(var H in G)S(C,H,{get:G[H],enumerable:!0,configurable:!0,set:function J(X){return G[H]=function(){return X}}})},x={lessThanXSeconds:{one:{regular:"m\xE9n\u011B ne\u017E 1 sekunda",past:"p\u0159ed m\xE9n\u011B ne\u017E 1 sekundou",future:"za m\xE9n\u011B ne\u017E 1 sekundu"},few:{regular:"m\xE9n\u011B ne\u017E {{count}} sekundy",past:"p\u0159ed m\xE9n\u011B ne\u017E {{count}} sekundami",future:"za m\xE9n\u011B ne\u017E {{count}} sekundy"},many:{regular:"m\xE9n\u011B ne\u017E {{count}} sekund",past:"p\u0159ed m\xE9n\u011B ne\u017E {{count}} sekundami",future:"za m\xE9n\u011B ne\u017E {{count}} sekund"}},xSeconds:{one:{regular:"1 sekunda",past:"p\u0159ed 1 sekundou",future:"za 1 sekundu"},few:{regular:"{{count}} sekundy",past:"p\u0159ed {{count}} sekundami",future:"za {{count}} sekundy"},many:{regular:"{{count}} sekund",past:"p\u0159ed {{count}} sekundami",future:"za {{count}} sekund"}},halfAMinute:{type:"other",other:{regular:"p\u016Fl minuty",past:"p\u0159ed p\u016Fl minutou",future:"za p\u016Fl minuty"}},lessThanXMinutes:{one:{regular:"m\xE9n\u011B ne\u017E 1 minuta",past:"p\u0159ed m\xE9n\u011B ne\u017E 1 minutou",future:"za m\xE9n\u011B ne\u017E 1 minutu"},few:{regular:"m\xE9n\u011B ne\u017E {{count}} minuty",past:"p\u0159ed m\xE9n\u011B ne\u017E {{count}} minutami",future:"za m\xE9n\u011B ne\u017E {{count}} minuty"},many:{regular:"m\xE9n\u011B ne\u017E {{count}} minut",past:"p\u0159ed m\xE9n\u011B ne\u017E {{count}} minutami",future:"za m\xE9n\u011B ne\u017E {{count}} minut"}},xMinutes:{one:{regular:"1 minuta",past:"p\u0159ed 1 minutou",future:"za 1 minutu"},few:{regular:"{{count}} minuty",past:"p\u0159ed {{count}} minutami",future:"za {{count}} minuty"},many:{regular:"{{count}} minut",past:"p\u0159ed {{count}} minutami",future:"za {{count}} minut"}},aboutXHours:{one:{regular:"p\u0159ibli\u017En\u011B 1 hodina",past:"p\u0159ibli\u017En\u011B p\u0159ed 1 hodinou",future:"p\u0159ibli\u017En\u011B za 1 hodinu"},few:{regular:"p\u0159ibli\u017En\u011B {{count}} hodiny",past:"p\u0159ibli\u017En\u011B p\u0159ed {{count}} hodinami",future:"p\u0159ibli\u017En\u011B za {{count}} hodiny"},many:{regular:"p\u0159ibli\u017En\u011B {{count}} hodin",past:"p\u0159ibli\u017En\u011B p\u0159ed {{count}} hodinami",future:"p\u0159ibli\u017En\u011B za {{count}} hodin"}},xHours:{one:{regular:"1 hodina",past:"p\u0159ed 1 hodinou",future:"za 1 hodinu"},few:{regular:"{{count}} hodiny",past:"p\u0159ed {{count}} hodinami",future:"za {{count}} hodiny"},many:{regular:"{{count}} hodin",past:"p\u0159ed {{count}} hodinami",future:"za {{count}} hodin"}},xDays:{one:{regular:"1 den",past:"p\u0159ed 1 dnem",future:"za 1 den"},few:{regular:"{{count}} dny",past:"p\u0159ed {{count}} dny",future:"za {{count}} dny"},many:{regular:"{{count}} dn\xED",past:"p\u0159ed {{count}} dny",future:"za {{count}} dn\xED"}},aboutXWeeks:{one:{regular:"p\u0159ibli\u017En\u011B 1 t\xFDden",past:"p\u0159ibli\u017En\u011B p\u0159ed 1 t\xFDdnem",future:"p\u0159ibli\u017En\u011B za 1 t\xFDden"},few:{regular:"p\u0159ibli\u017En\u011B {{count}} t\xFDdny",past:"p\u0159ibli\u017En\u011B p\u0159ed {{count}} t\xFDdny",future:"p\u0159ibli\u017En\u011B za {{count}} t\xFDdny"},many:{regular:"p\u0159ibli\u017En\u011B {{count}} t\xFDdn\u016F",past:"p\u0159ibli\u017En\u011B p\u0159ed {{count}} t\xFDdny",future:"p\u0159ibli\u017En\u011B za {{count}} t\xFDdn\u016F"}},xWeeks:{one:{regular:"1 t\xFDden",past:"p\u0159ed 1 t\xFDdnem",future:"za 1 t\xFDden"},few:{regular:"{{count}} t\xFDdny",past:"p\u0159ed {{count}} t\xFDdny",future:"za {{count}} t\xFDdny"},many:{regular:"{{count}} t\xFDdn\u016F",past:"p\u0159ed {{count}} t\xFDdny",future:"za {{count}} t\xFDdn\u016F"}},aboutXMonths:{one:{regular:"p\u0159ibli\u017En\u011B 1 m\u011Bs\xEDc",past:"p\u0159ibli\u017En\u011B p\u0159ed 1 m\u011Bs\xEDcem",future:"p\u0159ibli\u017En\u011B za 1 m\u011Bs\xEDc"},few:{regular:"p\u0159ibli\u017En\u011B {{count}} m\u011Bs\xEDce",past:"p\u0159ibli\u017En\u011B p\u0159ed {{count}} m\u011Bs\xEDci",future:"p\u0159ibli\u017En\u011B za {{count}} m\u011Bs\xEDce"},many:{regular:"p\u0159ibli\u017En\u011B {{count}} m\u011Bs\xEDc\u016F",past:"p\u0159ibli\u017En\u011B p\u0159ed {{count}} m\u011Bs\xEDci",future:"p\u0159ibli\u017En\u011B za {{count}} m\u011Bs\xEDc\u016F"}},xMonths:{one:{regular:"1 m\u011Bs\xEDc",past:"p\u0159ed 1 m\u011Bs\xEDcem",future:"za 1 m\u011Bs\xEDc"},few:{regular:"{{count}} m\u011Bs\xEDce",past:"p\u0159ed {{count}} m\u011Bs\xEDci",future:"za {{count}} m\u011Bs\xEDce"},many:{regular:"{{count}} m\u011Bs\xEDc\u016F",past:"p\u0159ed {{count}} m\u011Bs\xEDci",future:"za {{count}} m\u011Bs\xEDc\u016F"}},aboutXYears:{one:{regular:"p\u0159ibli\u017En\u011B 1 rok",past:"p\u0159ibli\u017En\u011B p\u0159ed 1 rokem",future:"p\u0159ibli\u017En\u011B za 1 rok"},few:{regular:"p\u0159ibli\u017En\u011B {{count}} roky",past:"p\u0159ibli\u017En\u011B p\u0159ed {{count}} roky",future:"p\u0159ibli\u017En\u011B za {{count}} roky"},many:{regular:"p\u0159ibli\u017En\u011B {{count}} rok\u016F",past:"p\u0159ibli\u017En\u011B p\u0159ed {{count}} roky",future:"p\u0159ibli\u017En\u011B za {{count}} rok\u016F"}},xYears:{one:{regular:"1 rok",past:"p\u0159ed 1 rokem",future:"za 1 rok"},few:{regular:"{{count}} roky",past:"p\u0159ed {{count}} roky",future:"za {{count}} roky"},many:{regular:"{{count}} rok\u016F",past:"p\u0159ed {{count}} roky",future:"za {{count}} rok\u016F"}},overXYears:{one:{regular:"v\xEDce ne\u017E 1 rok",past:"p\u0159ed v\xEDce ne\u017E 1 rokem",future:"za v\xEDce ne\u017E 1 rok"},few:{regular:"v\xEDce ne\u017E {{count}} roky",past:"p\u0159ed v\xEDce ne\u017E {{count}} roky",future:"za v\xEDce ne\u017E {{count}} roky"},many:{regular:"v\xEDce ne\u017E {{count}} rok\u016F",past:"p\u0159ed v\xEDce ne\u017E {{count}} roky",future:"za v\xEDce ne\u017E {{count}} rok\u016F"}},almostXYears:{one:{regular:"skoro 1 rok",past:"skoro p\u0159ed 1 rokem",future:"skoro za 1 rok"},few:{regular:"skoro {{count}} roky",past:"skoro p\u0159ed {{count}} roky",future:"skoro za {{count}} roky"},many:{regular:"skoro {{count}} rok\u016F",past:"skoro p\u0159ed {{count}} roky",future:"skoro za {{count}} rok\u016F"}}},D=function B(C,G,H){var J,X=x[C];if(X.type==="other")J=X.other;else if(G===1)J=X.one;else if(G>1&&G<5)J=X.few;else J=X.many;var Y=(H===null||H===void 0?void 0:H.addSuffix)===!0,Z=H===null||H===void 0?void 0:H.comparison,T;if(Y&&Z===-1)T=J.past;else if(Y&&Z===1)T=J.future;else T=J.regular;return T.replace("{{count}}",String(G))};function $(B){return function(){var C=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},G=C.width?String(C.width):B.defaultWidth,H=B.formats[G]||B.formats[B.defaultWidth];return H}}var M={full:"EEEE, d. MMMM yyyy",long:"d. MMMM yyyy",medium:"d. M. yyyy",short:"dd.MM.yyyy"},L={full:"H:mm:ss zzzz",long:"H:mm:ss z",medium:"H:mm:ss",short:"H:mm"},V={full:"{{date}} 'v' {{time}}",long:"{{date}} 'v' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},j={date:$({formats:M,defaultWidth:"full"}),time:$({formats:L,defaultWidth:"full"}),dateTime:$({formats:V,defaultWidth:"full"})},w=["ned\u011Bli","pond\u011Bl\xED","\xFAter\xFD","st\u0159edu","\u010Dtvrtek","p\xE1tek","sobotu"],R={lastWeek:"'posledn\xED' eeee 've' p",yesterday:"'v\u010Dera v' p",today:"'dnes v' p",tomorrow:"'z\xEDtra v' p",nextWeek:function B(C){var G=C.getDay();return"'v "+w[G]+" o' p"},other:"P"},_=function B(C,G){var H=R[C];if(typeof H==="function")return H(G);return H};function O(B){return function(C,G){var H=G!==null&&G!==void 0&&G.context?String(G.context):"standalone",J;if(H==="formatting"&&B.formattingValues){var X=B.defaultFormattingWidth||B.defaultWidth,Y=G!==null&&G!==void 0&&G.width?String(G.width):X;J=B.formattingValues[Y]||B.formattingValues[X]}else{var Z=B.defaultWidth,T=G!==null&&G!==void 0&&G.width?String(G.width):B.defaultWidth;J=B.values[T]||B.values[Z]}var U=B.argumentCallback?B.argumentCallback(C):C;return J[U]}}var F={narrow:["p\u0159. n. l.","n. l."],abbreviated:["p\u0159. n. l.","n. l."],wide:["p\u0159ed na\u0161\xEDm letopo\u010Dtem","na\u0161eho letopo\u010Dtu"]},P={narrow:["1","2","3","4"],abbreviated:["1. \u010Dtvrtlet\xED","2. \u010Dtvrtlet\xED","3. \u010Dtvrtlet\xED","4. \u010Dtvrtlet\xED"],wide:["1. \u010Dtvrtlet\xED","2. \u010Dtvrtlet\xED","3. \u010Dtvrtlet\xED","4. \u010Dtvrtlet\xED"]},v={narrow:["L","\xDA","B","D","K","\u010C","\u010C","S","Z","\u0158","L","P"],abbreviated:["led","\xFAno","b\u0159e","dub","kv\u011B","\u010Dvn","\u010Dvc","srp","z\xE1\u0159","\u0159\xEDj","lis","pro"],wide:["leden","\xFAnor","b\u0159ezen","duben","kv\u011Bten","\u010Derven","\u010Dervenec","srpen","z\xE1\u0159\xED","\u0159\xEDjen","listopad","prosinec"]},f={narrow:["L","\xDA","B","D","K","\u010C","\u010C","S","Z","\u0158","L","P"],abbreviated:["led","\xFAno","b\u0159e","dub","kv\u011B","\u010Dvn","\u010Dvc","srp","z\xE1\u0159","\u0159\xEDj","lis","pro"],wide:["ledna","\xFAnora","b\u0159ezna","dubna","kv\u011Btna","\u010Dervna","\u010Dervence","srpna","z\xE1\u0159\xED","\u0159\xEDjna","listopadu","prosince"]},k={narrow:["ne","po","\xFAt","st","\u010Dt","p\xE1","so"],short:["ne","po","\xFAt","st","\u010Dt","p\xE1","so"],abbreviated:["ned","pon","\xFAte","st\u0159","\u010Dtv","p\xE1t","sob"],wide:["ned\u011Ble","pond\u011Bl\xED","\xFAter\xFD","st\u0159eda","\u010Dtvrtek","p\xE1tek","sobota"]},b={narrow:{am:"dop.",pm:"odp.",midnight:"p\u016Flnoc",noon:"poledne",morning:"r\xE1no",afternoon:"odpoledne",evening:"ve\u010Der",night:"noc"},abbreviated:{am:"dop.",pm:"odp.",midnight:"p\u016Flnoc",noon:"poledne",morning:"r\xE1no",afternoon:"odpoledne",evening:"ve\u010Der",night:"noc"},wide:{am:"dopoledne",pm:"odpoledne",midnight:"p\u016Flnoc",noon:"poledne",morning:"r\xE1no",afternoon:"odpoledne",evening:"ve\u010Der",night:"noc"}},h={narrow:{am:"dop.",pm:"odp.",midnight:"p\u016Flnoc",noon:"poledne",morning:"r\xE1no",afternoon:"odpoledne",evening:"ve\u010Der",night:"noc"},abbreviated:{am:"dop.",pm:"odp.",midnight:"p\u016Flnoc",noon:"poledne",morning:"r\xE1no",afternoon:"odpoledne",evening:"ve\u010Der",night:"noc"},wide:{am:"dopoledne",pm:"odpoledne",midnight:"p\u016Flnoc",noon:"poledne",morning:"r\xE1no",afternoon:"odpoledne",evening:"ve\u010Der",night:"noc"}},y=function B(C,G){var H=Number(C);return H+"."},m={ordinalNumber:y,era:O({values:F,defaultWidth:"wide"}),quarter:O({values:P,defaultWidth:"wide",argumentCallback:function B(C){return C-1}}),month:O({values:v,defaultWidth:"wide",formattingValues:f,defaultFormattingWidth:"wide"}),day:O({values:k,defaultWidth:"wide"}),dayPeriod:O({values:b,defaultWidth:"wide",formattingValues:h,defaultFormattingWidth:"wide"})};function Q(B){return function(C){var G=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},H=G.width,J=H&&B.matchPatterns[H]||B.matchPatterns[B.defaultMatchWidth],X=C.match(J);if(!X)return null;var Y=X[0],Z=H&&B.parsePatterns[H]||B.parsePatterns[B.defaultParseWidth],T=Array.isArray(Z)?g(Z,function(K){return K.test(Y)}):c(Z,function(K){return K.test(Y)}),U;U=B.valueCallback?B.valueCallback(T):T,U=G.valueCallback?G.valueCallback(U):U;var HB=C.slice(Y.length);return{value:U,rest:HB}}}function c(B,C){for(var G in B)if(Object.prototype.hasOwnProperty.call(B,G)&&C(B[G]))return G;return}function g(B,C){for(var G=0;G<B.length;G++)if(C(B[G]))return G;return}function d(B){return function(C){var G=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},H=C.match(B.matchPattern);if(!H)return null;var J=H[0],X=C.match(B.parsePattern);if(!X)return null;var Y=B.valueCallback?B.valueCallback(X[0]):X[0];Y=G.valueCallback?G.valueCallback(Y):Y;var Z=C.slice(J.length);return{value:Y,rest:Z}}}var p=/^(\d+)\.?/i,u=/\d+/i,l={narrow:/^(p[řr](\.|ed) Kr\.|p[řr](\.|ed) n\. l\.|po Kr\.|n\. l\.)/i,abbreviated:/^(p[řr](\.|ed) Kr\.|p[řr](\.|ed) n\. l\.|po Kr\.|n\. l\.)/i,wide:/^(p[řr](\.|ed) Kristem|p[řr](\.|ed) na[šs][íi]m letopo[čc]tem|po Kristu|na[šs]eho letopo[čc]tu)/i},i={any:[/^p[řr]/i,/^(po|n)/i]},n={narrow:/^[1234]/i,abbreviated:/^[1234]\. [čc]tvrtlet[íi]/i,wide:/^[1234]\. [čc]tvrtlet[íi]/i},s={any:[/1/i,/2/i,/3/i,/4/i]},o={narrow:/^[lúubdkčcszřrlp]/i,abbreviated:/^(led|[úu]no|b[řr]e|dub|kv[ěe]|[čc]vn|[čc]vc|srp|z[áa][řr]|[řr][íi]j|lis|pro)/i,wide:/^(leden|ledna|[úu]nora?|b[řr]ezen|b[řr]ezna|duben|dubna|kv[ěe]ten|kv[ěe]tna|[čc]erven(ec|ce)?|[čc]ervna|srpen|srpna|z[áa][řr][íi]|[řr][íi]jen|[řr][íi]jna|listopad(a|u)?|prosinec|prosince)/i},r={narrow:[/^l/i,/^[úu]/i,/^b/i,/^d/i,/^k/i,/^[čc]/i,/^[čc]/i,/^s/i,/^z/i,/^[řr]/i,/^l/i,/^p/i],any:[/^led/i,/^[úu]n/i,/^b[řr]e/i,/^dub/i,/^kv[ěe]/i,/^[čc]vn|[čc]erven(?!\w)|[čc]ervna/i,/^[čc]vc|[čc]erven(ec|ce)/i,/^srp/i,/^z[áa][řr]/i,/^[řr][íi]j/i,/^lis/i,/^pro/i]},e={narrow:/^[npuúsčps]/i,short:/^(ne|po|[úu]t|st|[čc]t|p[áa]|so)/i,abbreviated:/^(ned|pon|[úu]te|st[rř]|[čc]tv|p[áa]t|sob)/i,wide:/^(ned[ěe]le|pond[ěe]l[íi]|[úu]ter[ýy]|st[řr]eda|[čc]tvrtek|p[áa]tek|sobota)/i},a={narrow:[/^n/i,/^p/i,/^[úu]/i,/^s/i,/^[čc]/i,/^p/i,/^s/i],any:[/^ne/i,/^po/i,/^[úu]t/i,/^st/i,/^[čc]t/i,/^p[áa]/i,/^so/i]},t={any:/^dopoledne|dop\.?|odpoledne|odp\.?|p[ůu]lnoc|poledne|r[áa]no|odpoledne|ve[čc]er|(v )?noci?/i},BB={any:{am:/^dop/i,pm:/^odp/i,midnight:/^p[ůu]lnoc/i,noon:/^poledne/i,morning:/r[áa]no/i,afternoon:/odpoledne/i,evening:/ve[čc]er/i,night:/noc/i}},CB={ordinalNumber:d({matchPattern:p,parsePattern:u,valueCallback:function B(C){return parseInt(C,10)}}),era:Q({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any"}),quarter:Q({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any",valueCallback:function B(C){return C+1}}),month:Q({matchPatterns:o,defaultMatchWidth:"wide",parsePatterns:r,defaultParseWidth:"any"}),day:Q({matchPatterns:e,defaultMatchWidth:"wide",parsePatterns:a,defaultParseWidth:"any"}),dayPeriod:Q({matchPatterns:t,defaultMatchWidth:"any",parsePatterns:BB,defaultParseWidth:"any"})},GB={code:"cs",formatDistance:D,formatLong:j,formatRelative:_,localize:m,match:CB,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=q(q({},window.dateFns),{},{locale:q(q({},(A=window.dateFns)===null||A===void 0?void 0:A.locale),{},{cs:GB})})})();

//# debugId=585BCDC3F917766964756E2164756E21
