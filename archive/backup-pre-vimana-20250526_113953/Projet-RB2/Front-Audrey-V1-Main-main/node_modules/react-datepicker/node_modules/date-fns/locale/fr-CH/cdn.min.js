(()=>{var A;function Q(B){return Q=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(X){return typeof X}:function(X){return X&&typeof Symbol=="function"&&X.constructor===Symbol&&X!==Symbol.prototype?"symbol":typeof X},Q(B)}function O(B,X){var J=Object.keys(B);if(Object.getOwnPropertySymbols){var Y=Object.getOwnPropertySymbols(B);X&&(Y=Y.filter(function(Z){return Object.getOwnPropertyDescriptor(B,Z).enumerable})),J.push.apply(J,Y)}return J}function $(B){for(var X=1;X<arguments.length;X++){var J=arguments[X]!=null?arguments[X]:{};X%2?O(Object(J),!0).forEach(function(Y){z(B,Y,J[Y])}):Object.getOwnPropertyDescriptors?Object.defineProperties(B,Object.getOwnPropertyDescriptors(J)):O(Object(J)).forEach(function(Y){Object.defineProperty(B,Y,Object.getOwnPropertyDescriptor(J,Y))})}return B}function z(B,X,J){if(X=D(X),X in B)Object.defineProperty(B,X,{value:J,enumerable:!0,configurable:!0,writable:!0});else B[X]=J;return B}function D(B){var X=W(B,"string");return Q(X)=="symbol"?X:String(X)}function W(B,X){if(Q(B)!="object"||!B)return B;var J=B[Symbol.toPrimitive];if(J!==void 0){var Y=J.call(B,X||"default");if(Q(Y)!="object")return Y;throw new TypeError("@@toPrimitive must return a primitive value.")}return(X==="string"?String:Number)(B)}var V=Object.defineProperty,XB=function B(X,J){for(var Y in J)V(X,Y,{get:J[Y],enumerable:!0,configurable:!0,set:function Z(G){return J[Y]=function(){return G}}})},x={lessThanXSeconds:{one:"moins d\u2019une seconde",other:"moins de {{count}} secondes"},xSeconds:{one:"1 seconde",other:"{{count}} secondes"},halfAMinute:"30 secondes",lessThanXMinutes:{one:"moins d\u2019une minute",other:"moins de {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"environ 1 heure",other:"environ {{count}} heures"},xHours:{one:"1 heure",other:"{{count}} heures"},xDays:{one:"1 jour",other:"{{count}} jours"},aboutXWeeks:{one:"environ 1 semaine",other:"environ {{count}} semaines"},xWeeks:{one:"1 semaine",other:"{{count}} semaines"},aboutXMonths:{one:"environ 1 mois",other:"environ {{count}} mois"},xMonths:{one:"1 mois",other:"{{count}} mois"},aboutXYears:{one:"environ 1 an",other:"environ {{count}} ans"},xYears:{one:"1 an",other:"{{count}} ans"},overXYears:{one:"plus d\u2019un an",other:"plus de {{count}} ans"},almostXYears:{one:"presqu\u2019un an",other:"presque {{count}} ans"}},N=function B(X,J,Y){var Z,G=x[X];if(typeof G==="string")Z=G;else if(J===1)Z=G.one;else Z=G.other.replace("{{count}}",String(J));if(Y!==null&&Y!==void 0&&Y.addSuffix)if(Y.comparison&&Y.comparison>0)return"dans "+Z;else return"il y a "+Z;return Z};function U(B){return function(X,J){var Y=J!==null&&J!==void 0&&J.context?String(J.context):"standalone",Z;if(Y==="formatting"&&B.formattingValues){var G=B.defaultFormattingWidth||B.defaultWidth,C=J!==null&&J!==void 0&&J.width?String(J.width):G;Z=B.formattingValues[C]||B.formattingValues[G]}else{var H=B.defaultWidth,T=J!==null&&J!==void 0&&J.width?String(J.width):B.defaultWidth;Z=B.values[T]||B.values[H]}var I=B.argumentCallback?B.argumentCallback(X):X;return Z[I]}}var R={narrow:["av. J.-C","ap. J.-C"],abbreviated:["av. J.-C","ap. J.-C"],wide:["avant J\xE9sus-Christ","apr\xE8s J\xE9sus-Christ"]},S={narrow:["T1","T2","T3","T4"],abbreviated:["1er trim.","2\xE8me trim.","3\xE8me trim.","4\xE8me trim."],wide:["1er trimestre","2\xE8me trimestre","3\xE8me trimestre","4\xE8me trimestre"]},M={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["janv.","f\xE9vr.","mars","avr.","mai","juin","juil.","ao\xFBt","sept.","oct.","nov.","d\xE9c."],wide:["janvier","f\xE9vrier","mars","avril","mai","juin","juillet","ao\xFBt","septembre","octobre","novembre","d\xE9cembre"]},L={narrow:["D","L","M","M","J","V","S"],short:["di","lu","ma","me","je","ve","sa"],abbreviated:["dim.","lun.","mar.","mer.","jeu.","ven.","sam."],wide:["dimanche","lundi","mardi","mercredi","jeudi","vendredi","samedi"]},j={narrow:{am:"AM",pm:"PM",midnight:"minuit",noon:"midi",morning:"mat.",afternoon:"ap.m.",evening:"soir",night:"mat."},abbreviated:{am:"AM",pm:"PM",midnight:"minuit",noon:"midi",morning:"matin",afternoon:"apr\xE8s-midi",evening:"soir",night:"matin"},wide:{am:"AM",pm:"PM",midnight:"minuit",noon:"midi",morning:"du matin",afternoon:"de l\u2019apr\xE8s-midi",evening:"du soir",night:"du matin"}},w=function B(X,J){var Y=Number(X),Z=J===null||J===void 0?void 0:J.unit;if(Y===0)return"0";var G=["year","week","hour","minute","second"],C;if(Y===1)C=Z&&G.includes(Z)?"\xE8re":"er";else C="\xE8me";return Y+C},_=["MMM","MMMM"],P={preprocessor:function B(X,J){if(X.getDate()===1)return J;var Y=J.some(function(Z){return Z.isToken&&_.includes(Z.value)});if(!Y)return J;return J.map(function(Z){return Z.isToken&&Z.value==="do"?{isToken:!0,value:"d"}:Z})},ordinalNumber:w,era:U({values:R,defaultWidth:"wide"}),quarter:U({values:S,defaultWidth:"wide",argumentCallback:function B(X){return X-1}}),month:U({values:M,defaultWidth:"wide"}),day:U({values:L,defaultWidth:"wide"}),dayPeriod:U({values:j,defaultWidth:"wide"})};function q(B){return function(X){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},Y=J.width,Z=Y&&B.matchPatterns[Y]||B.matchPatterns[B.defaultMatchWidth],G=X.match(Z);if(!G)return null;var C=G[0],H=Y&&B.parsePatterns[Y]||B.parsePatterns[B.defaultParseWidth],T=Array.isArray(H)?F(H,function(K){return K.test(C)}):v(H,function(K){return K.test(C)}),I;I=B.valueCallback?B.valueCallback(T):T,I=J.valueCallback?J.valueCallback(I):I;var JB=X.slice(C.length);return{value:I,rest:JB}}}function v(B,X){for(var J in B)if(Object.prototype.hasOwnProperty.call(B,J)&&X(B[J]))return J;return}function F(B,X){for(var J=0;J<B.length;J++)if(X(B[J]))return J;return}function k(B){return function(X){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},Y=X.match(B.matchPattern);if(!Y)return null;var Z=Y[0],G=X.match(B.parsePattern);if(!G)return null;var C=B.valueCallback?B.valueCallback(G[0]):G[0];C=J.valueCallback?J.valueCallback(C):C;var H=X.slice(Z.length);return{value:C,rest:H}}}var f=/^(\d+)(ième|ère|ème|er|e)?/i,b=/\d+/i,h={narrow:/^(av\.J\.C|ap\.J\.C|ap\.J\.-C)/i,abbreviated:/^(av\.J\.-C|av\.J-C|apr\.J\.-C|apr\.J-C|ap\.J-C)/i,wide:/^(avant Jésus-Christ|après Jésus-Christ)/i},m={any:[/^av/i,/^ap/i]},y={narrow:/^T?[1234]/i,abbreviated:/^[1234](er|ème|e)? trim\.?/i,wide:/^[1234](er|ème|e)? trimestre/i},c={any:[/1/i,/2/i,/3/i,/4/i]},g={narrow:/^[jfmasond]/i,abbreviated:/^(janv|févr|mars|avr|mai|juin|juill|juil|août|sept|oct|nov|déc)\.?/i,wide:/^(janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)/i},d={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^av/i,/^ma/i,/^juin/i,/^juil/i,/^ao/i,/^s/i,/^o/i,/^n/i,/^d/i]},u={narrow:/^[lmjvsd]/i,short:/^(di|lu|ma|me|je|ve|sa)/i,abbreviated:/^(dim|lun|mar|mer|jeu|ven|sam)\.?/i,wide:/^(dimanche|lundi|mardi|mercredi|jeudi|vendredi|samedi)/i},l={narrow:[/^d/i,/^l/i,/^m/i,/^m/i,/^j/i,/^v/i,/^s/i],any:[/^di/i,/^lu/i,/^ma/i,/^me/i,/^je/i,/^ve/i,/^sa/i]},p={narrow:/^(a|p|minuit|midi|mat\.?|ap\.?m\.?|soir|nuit)/i,any:/^([ap]\.?\s?m\.?|du matin|de l'après[-\s]midi|du soir|de la nuit)/i},i={any:{am:/^a/i,pm:/^p/i,midnight:/^min/i,noon:/^mid/i,morning:/mat/i,afternoon:/ap/i,evening:/soir/i,night:/nuit/i}},n={ordinalNumber:k({matchPattern:f,parsePattern:b,valueCallback:function B(X){return parseInt(X)}}),era:q({matchPatterns:h,defaultMatchWidth:"wide",parsePatterns:m,defaultParseWidth:"any"}),quarter:q({matchPatterns:y,defaultMatchWidth:"wide",parsePatterns:c,defaultParseWidth:"any",valueCallback:function B(X){return X+1}}),month:q({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:d,defaultParseWidth:"any"}),day:q({matchPatterns:u,defaultMatchWidth:"wide",parsePatterns:l,defaultParseWidth:"any"}),dayPeriod:q({matchPatterns:p,defaultMatchWidth:"any",parsePatterns:i,defaultParseWidth:"any"})};function E(B){return function(){var X=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},J=X.width?String(X.width):B.defaultWidth,Y=B.formats[J]||B.formats[B.defaultWidth];return Y}}var s={full:"EEEE d MMMM y",long:"d MMMM y",medium:"d MMM y",short:"dd.MM.y"},o={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},e={full:"{{date}} '\xE0' {{time}}",long:"{{date}} '\xE0' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},r={date:E({formats:s,defaultWidth:"full"}),time:E({formats:o,defaultWidth:"full"}),dateTime:E({formats:e,defaultWidth:"full"})},a={lastWeek:"eeee 'la semaine derni\xE8re \xE0' p",yesterday:"'hier \xE0' p",today:"'aujourd\u2019hui \xE0' p",tomorrow:"'demain \xE0' p'",nextWeek:"eeee 'la semaine prochaine \xE0' p",other:"P"},t=function B(X,J,Y,Z){return a[X]},BB={code:"fr-CH",formatDistance:N,formatLong:r,formatRelative:t,localize:P,match:n,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=$($({},window.dateFns),{},{locale:$($({},(A=window.dateFns)===null||A===void 0?void 0:A.locale),{},{frCH:BB})})})();

//# debugId=66DAE7AB9A5764CC64756E2164756E21
