(()=>{var $;function O(H){return O=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(J){return typeof J}:function(J){return J&&typeof Symbol=="function"&&J.constructor===Symbol&&J!==Symbol.prototype?"symbol":typeof J},O(H)}function E(H,J){var X=Object.keys(H);if(Object.getOwnPropertySymbols){var Y=Object.getOwnPropertySymbols(H);J&&(Y=Y.filter(function(U){return Object.getOwnPropertyDescriptor(H,U).enumerable})),X.push.apply(X,Y)}return X}function A(H){for(var J=1;J<arguments.length;J++){var X=arguments[J]!=null?arguments[J]:{};J%2?E(Object(X),!0).forEach(function(Y){N(H,Y,X[Y])}):Object.getOwnPropertyDescriptors?Object.defineProperties(H,Object.getOwnPropertyDescriptors(X)):E(Object(X)).forEach(function(Y){Object.defineProperty(H,Y,Object.getOwnPropertyDescriptor(X,Y))})}return H}function N(H,J,X){if(J=z(J),J in H)Object.defineProperty(H,J,{value:X,enumerable:!0,configurable:!0,writable:!0});else H[J]=X;return H}function z(H){var J=G(H,"string");return O(J)=="symbol"?J:String(J)}function G(H,J){if(O(H)!="object"||!H)return H;var X=H[Symbol.toPrimitive];if(X!==void 0){var Y=X.call(H,J||"default");if(O(Y)!="object")return Y;throw new TypeError("@@toPrimitive must return a primitive value.")}return(J==="string"?String:Number)(H)}var W=Object.defineProperty,XH=function H(J,X){for(var Y in X)W(J,Y,{get:X[Y],enumerable:!0,configurable:!0,set:function U(T){return X[Y]=function(){return T}}})},S={lessThanXSeconds:{one:"\u0623\u0642\u0644 \u0645\u0646 \u062B\u0627\u0646\u064A\u0629 \u0648\u0627\u062D\u062F\u0629",two:"\u0623\u0642\u0644 \u0645\u0646 \u062B\u0627\u0646\u062A\u064A\u0646",threeToTen:"\u0623\u0642\u0644 \u0645\u0646 {{count}} \u062B\u0648\u0627\u0646\u064A",other:"\u0623\u0642\u0644 \u0645\u0646 {{count}} \u062B\u0627\u0646\u064A\u0629"},xSeconds:{one:"\u062B\u0627\u0646\u064A\u0629 \u0648\u0627\u062D\u062F\u0629",two:"\u062B\u0627\u0646\u062A\u064A\u0646",threeToTen:"{{count}} \u062B\u0648\u0627\u0646\u064A",other:"{{count}} \u062B\u0627\u0646\u064A\u0629"},halfAMinute:"\u0646\u0635\u0641 \u062F\u0642\u064A\u0642\u0629",lessThanXMinutes:{one:"\u0623\u0642\u0644 \u0645\u0646 \u062F\u0642\u064A\u0642\u0629",two:"\u0623\u0642\u0644 \u0645\u0646 \u062F\u0642\u064A\u0642\u062A\u064A\u0646",threeToTen:"\u0623\u0642\u0644 \u0645\u0646 {{count}} \u062F\u0642\u0627\u0626\u0642",other:"\u0623\u0642\u0644 \u0645\u0646 {{count}} \u062F\u0642\u064A\u0642\u0629"},xMinutes:{one:"\u062F\u0642\u064A\u0642\u0629 \u0648\u0627\u062D\u062F\u0629",two:"\u062F\u0642\u064A\u0642\u062A\u064A\u0646",threeToTen:"{{count}} \u062F\u0642\u0627\u0626\u0642",other:"{{count}} \u062F\u0642\u064A\u0642\u0629"},aboutXHours:{one:"\u0633\u0627\u0639\u0629 \u0648\u0627\u062D\u062F\u0629 \u062A\u0642\u0631\u064A\u0628\u0627\u064B",two:"\u0633\u0627\u0639\u062A\u064A\u0646 \u062A\u0642\u0631\u064A\u0628\u0627\u064B",threeToTen:"{{count}} \u0633\u0627\u0639\u0627\u062A \u062A\u0642\u0631\u064A\u0628\u0627\u064B",other:"{{count}} \u0633\u0627\u0639\u0629 \u062A\u0642\u0631\u064A\u0628\u0627\u064B"},xHours:{one:"\u0633\u0627\u0639\u0629 \u0648\u0627\u062D\u062F\u0629",two:"\u0633\u0627\u0639\u062A\u064A\u0646",threeToTen:"{{count}} \u0633\u0627\u0639\u0627\u062A",other:"{{count}} \u0633\u0627\u0639\u0629"},xDays:{one:"\u064A\u0648\u0645 \u0648\u0627\u062D\u062F",two:"\u064A\u0648\u0645\u064A\u0646",threeToTen:"{{count}} \u0623\u064A\u0627\u0645",other:"{{count}} \u064A\u0648\u0645"},aboutXWeeks:{one:"\u0623\u0633\u0628\u0648\u0639 \u0648\u0627\u062D\u062F \u062A\u0642\u0631\u064A\u0628\u0627\u064B",two:"\u0623\u0633\u0628\u0648\u0639\u064A\u0646 \u062A\u0642\u0631\u064A\u0628\u0627\u064B",threeToTen:"{{count}} \u0623\u0633\u0627\u0628\u064A\u0639 \u062A\u0642\u0631\u064A\u0628\u0627\u064B",other:"{{count}} \u0623\u0633\u0628\u0648\u0639 \u062A\u0642\u0631\u064A\u0628\u0627\u064B"},xWeeks:{one:"\u0623\u0633\u0628\u0648\u0639 \u0648\u0627\u062D\u062F",two:"\u0623\u0633\u0628\u0648\u0639\u064A\u0646",threeToTen:"{{count}} \u0623\u0633\u0627\u0628\u064A\u0639",other:"{{count}} \u0623\u0633\u0628\u0648\u0639"},aboutXMonths:{one:"\u0634\u0647\u0631 \u0648\u0627\u062D\u062F \u062A\u0642\u0631\u064A\u0628\u0627\u064B",two:"\u0634\u0647\u0631\u064A\u0646 \u062A\u0642\u0631\u064A\u0628\u0627\u064B",threeToTen:"{{count}} \u0623\u0634\u0647\u0631 \u062A\u0642\u0631\u064A\u0628\u0627\u064B",other:"{{count}} \u0634\u0647\u0631 \u062A\u0642\u0631\u064A\u0628\u0627\u064B"},xMonths:{one:"\u0634\u0647\u0631 \u0648\u0627\u062D\u062F",two:"\u0634\u0647\u0631\u064A\u0646",threeToTen:"{{count}} \u0623\u0634\u0647\u0631",other:"{{count}} \u0634\u0647\u0631"},aboutXYears:{one:"\u0639\u0627\u0645 \u0648\u0627\u062D\u062F \u062A\u0642\u0631\u064A\u0628\u0627\u064B",two:"\u0639\u0627\u0645\u064A\u0646 \u062A\u0642\u0631\u064A\u0628\u0627\u064B",threeToTen:"{{count}} \u0623\u0639\u0648\u0627\u0645 \u062A\u0642\u0631\u064A\u0628\u0627\u064B",other:"{{count}} \u0639\u0627\u0645 \u062A\u0642\u0631\u064A\u0628\u0627\u064B"},xYears:{one:"\u0639\u0627\u0645 \u0648\u0627\u062D\u062F",two:"\u0639\u0627\u0645\u064A\u0646",threeToTen:"{{count}} \u0623\u0639\u0648\u0627\u0645",other:"{{count}} \u0639\u0627\u0645"},overXYears:{one:"\u0623\u0643\u062B\u0631 \u0645\u0646 \u0639\u0627\u0645",two:"\u0623\u0643\u062B\u0631 \u0645\u0646 \u0639\u0627\u0645\u064A\u0646",threeToTen:"\u0623\u0643\u062B\u0631 \u0645\u0646 {{count}} \u0623\u0639\u0648\u0627\u0645",other:"\u0623\u0643\u062B\u0631 \u0645\u0646 {{count}} \u0639\u0627\u0645"},almostXYears:{one:"\u0639\u0627\u0645 \u0648\u0627\u062D\u062F \u062A\u0642\u0631\u064A\u0628\u0627\u064B",two:"\u0639\u0627\u0645\u064A\u0646 \u062A\u0642\u0631\u064A\u0628\u0627\u064B",threeToTen:"{{count}} \u0623\u0639\u0648\u0627\u0645 \u062A\u0642\u0631\u064A\u0628\u0627\u064B",other:"{{count}} \u0639\u0627\u0645 \u062A\u0642\u0631\u064A\u0628\u0627\u064B"}},V=function H(J,X,Y){Y=Y||{};var U=S[J],T;if(typeof U==="string")T=U;else if(X===1)T=U.one;else if(X===2)T=U.two;else if(X<=10)T=U.threeToTen.replace("{{count}}",String(X));else T=U.other.replace("{{count}}",String(X));if(Y.addSuffix)if(Y.comparison&&Y.comparison>0)return"\u0641\u064A \u062E\u0644\u0627\u0644 "+T;else return"\u0645\u0646\u0630 "+T;return T};function K(H){return function(){var J=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},X=J.width?String(J.width):H.defaultWidth,Y=H.formats[X]||H.formats[H.defaultWidth];return Y}}var M={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},R={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},D={full:"{{date}} '\u0639\u0646\u062F' {{time}}",long:"{{date}} '\u0639\u0646\u062F' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},L={date:K({formats:M,defaultWidth:"full"}),time:K({formats:R,defaultWidth:"full"}),dateTime:K({formats:D,defaultWidth:"full"})},j={lastWeek:"'\u0623\u062E\u0631' eeee '\u0639\u0646\u062F' p",yesterday:"'\u0623\u0645\u0633 \u0639\u0646\u062F' p",today:"'\u0627\u0644\u064A\u0648\u0645 \u0639\u0646\u062F' p",tomorrow:"'\u063A\u062F\u0627\u064B \u0639\u0646\u062F' p",nextWeek:"eeee '\u0639\u0646\u062F' p",other:"P"},w=function H(J,X,Y,U){return j[J]};function Q(H){return function(J,X){var Y=X!==null&&X!==void 0&&X.context?String(X.context):"standalone",U;if(Y==="formatting"&&H.formattingValues){var T=H.defaultFormattingWidth||H.defaultWidth,Z=X!==null&&X!==void 0&&X.width?String(X.width):T;U=H.formattingValues[Z]||H.formattingValues[T]}else{var B=H.defaultWidth,C=X!==null&&X!==void 0&&X.width?String(X.width):H.defaultWidth;U=H.values[C]||H.values[B]}var I=H.argumentCallback?H.argumentCallback(J):J;return U[I]}}var _={narrow:["\u0642","\u0628"],abbreviated:["\u0642.\u0645.","\u0628.\u0645."],wide:["\u0642\u0628\u0644 \u0627\u0644\u0645\u064A\u0644\u0627\u062F","\u0628\u0639\u062F \u0627\u0644\u0645\u064A\u0644\u0627\u062F"]},f={narrow:["1","2","3","4"],abbreviated:["\u06311","\u06312","\u06313","\u06314"],wide:["\u0627\u0644\u0631\u0628\u0639 \u0627\u0644\u0623\u0648\u0644","\u0627\u0644\u0631\u0628\u0639 \u0627\u0644\u062B\u0627\u0646\u064A","\u0627\u0644\u0631\u0628\u0639 \u0627\u0644\u062B\u0627\u0644\u062B","\u0627\u0644\u0631\u0628\u0639 \u0627\u0644\u0631\u0627\u0628\u0639"]},F={narrow:["\u062C","\u0641","\u0645","\u0623","\u0645","\u062C","\u062C","\u0623","\u0633","\u0623","\u0646","\u062F"],abbreviated:["\u062C\u0627\u0646\u0640","\u0641\u064A\u0641\u0640","\u0645\u0627\u0631\u0633","\u0623\u0641\u0631\u064A\u0644","\u0645\u0627\u064A\u0640","\u062C\u0648\u0627\u0646\u0640","\u062C\u0648\u064A\u0640","\u0623\u0648\u062A","\u0633\u0628\u062A\u0640","\u0623\u0643\u062A\u0640","\u0646\u0648\u0641\u0640","\u062F\u064A\u0633\u0640"],wide:["\u062C\u0627\u0646\u0641\u064A","\u0641\u064A\u0641\u0631\u064A","\u0645\u0627\u0631\u0633","\u0623\u0641\u0631\u064A\u0644","\u0645\u0627\u064A","\u062C\u0648\u0627\u0646","\u062C\u0648\u064A\u0644\u064A\u0629","\u0623\u0648\u062A","\u0633\u0628\u062A\u0645\u0628\u0631","\u0623\u0643\u062A\u0648\u0628\u0631","\u0646\u0648\u0641\u0645\u0628\u0631","\u062F\u064A\u0633\u0645\u0628\u0631"]},v={narrow:["\u062D","\u0646","\u062B","\u0631","\u062E","\u062C","\u0633"],short:["\u0623\u062D\u062F","\u0627\u062B\u0646\u064A\u0646","\u062B\u0644\u0627\u062B\u0627\u0621","\u0623\u0631\u0628\u0639\u0627\u0621","\u062E\u0645\u064A\u0633","\u062C\u0645\u0639\u0629","\u0633\u0628\u062A"],abbreviated:["\u0623\u062D\u062F","\u0627\u062B\u0646\u0640","\u062B\u0644\u0627","\u0623\u0631\u0628\u0640","\u062E\u0645\u064A\u0640","\u062C\u0645\u0639\u0629","\u0633\u0628\u062A"],wide:["\u0627\u0644\u0623\u062D\u062F","\u0627\u0644\u0627\u062B\u0646\u064A\u0646","\u0627\u0644\u062B\u0644\u0627\u062B\u0627\u0621","\u0627\u0644\u0623\u0631\u0628\u0639\u0627\u0621","\u0627\u0644\u062E\u0645\u064A\u0633","\u0627\u0644\u062C\u0645\u0639\u0629","\u0627\u0644\u0633\u0628\u062A"]},P={narrow:{am:"\u0635",pm:"\u0645",midnight:"\u0646",noon:"\u0638",morning:"\u0635\u0628\u0627\u062D\u0627\u064B",afternoon:"\u0628\u0639\u062F \u0627\u0644\u0638\u0647\u0631",evening:"\u0645\u0633\u0627\u0621\u0627\u064B",night:"\u0644\u064A\u0644\u0627\u064B"},abbreviated:{am:"\u0635",pm:"\u0645",midnight:"\u0646\u0635\u0641 \u0627\u0644\u0644\u064A\u0644",noon:"\u0638\u0647\u0631",morning:"\u0635\u0628\u0627\u062D\u0627\u064B",afternoon:"\u0628\u0639\u062F \u0627\u0644\u0638\u0647\u0631",evening:"\u0645\u0633\u0627\u0621\u0627\u064B",night:"\u0644\u064A\u0644\u0627\u064B"},wide:{am:"\u0635",pm:"\u0645",midnight:"\u0646\u0635\u0641 \u0627\u0644\u0644\u064A\u0644",noon:"\u0638\u0647\u0631",morning:"\u0635\u0628\u0627\u062D\u0627\u064B",afternoon:"\u0628\u0639\u062F \u0627\u0644\u0638\u0647\u0631",evening:"\u0645\u0633\u0627\u0621\u0627\u064B",night:"\u0644\u064A\u0644\u0627\u064B"}},k={narrow:{am:"\u0635",pm:"\u0645",midnight:"\u0646",noon:"\u0638",morning:"\u0641\u064A \u0627\u0644\u0635\u0628\u0627\u062D",afternoon:"\u0628\u0639\u062F \u0627\u0644\u0638\u0640\u0647\u0631",evening:"\u0641\u064A \u0627\u0644\u0645\u0633\u0627\u0621",night:"\u0641\u064A \u0627\u0644\u0644\u064A\u0644"},abbreviated:{am:"\u0635",pm:"\u0645",midnight:"\u0646\u0635\u0641 \u0627\u0644\u0644\u064A\u0644",noon:"\u0638\u0647\u0631",morning:"\u0641\u064A \u0627\u0644\u0635\u0628\u0627\u062D",afternoon:"\u0628\u0639\u062F \u0627\u0644\u0638\u0647\u0631",evening:"\u0641\u064A \u0627\u0644\u0645\u0633\u0627\u0621",night:"\u0641\u064A \u0627\u0644\u0644\u064A\u0644"},wide:{am:"\u0635",pm:"\u0645",midnight:"\u0646\u0635\u0641 \u0627\u0644\u0644\u064A\u0644",noon:"\u0638\u0647\u0631",morning:"\u0635\u0628\u0627\u062D\u0627\u064B",afternoon:"\u0628\u0639\u062F \u0627\u0644\u0638\u0640\u0647\u0631",evening:"\u0641\u064A \u0627\u0644\u0645\u0633\u0627\u0621",night:"\u0641\u064A \u0627\u0644\u0644\u064A\u0644"}},b=function H(J){return String(J)},h={ordinalNumber:b,era:Q({values:_,defaultWidth:"wide"}),quarter:Q({values:f,defaultWidth:"wide",argumentCallback:function H(J){return Number(J)-1}}),month:Q({values:F,defaultWidth:"wide"}),day:Q({values:v,defaultWidth:"wide"}),dayPeriod:Q({values:P,defaultWidth:"wide",formattingValues:k,defaultFormattingWidth:"wide"})};function m(H){return function(J){var X=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},Y=J.match(H.matchPattern);if(!Y)return null;var U=Y[0],T=J.match(H.parsePattern);if(!T)return null;var Z=H.valueCallback?H.valueCallback(T[0]):T[0];Z=X.valueCallback?X.valueCallback(Z):Z;var B=J.slice(U.length);return{value:Z,rest:B}}}function q(H){return function(J){var X=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},Y=X.width,U=Y&&H.matchPatterns[Y]||H.matchPatterns[H.defaultMatchWidth],T=J.match(U);if(!T)return null;var Z=T[0],B=Y&&H.parsePatterns[Y]||H.parsePatterns[H.defaultParseWidth],C=Array.isArray(B)?c(B,function(x){return x.test(Z)}):y(B,function(x){return x.test(Z)}),I;I=H.valueCallback?H.valueCallback(C):C,I=X.valueCallback?X.valueCallback(I):I;var JH=J.slice(Z.length);return{value:I,rest:JH}}}function y(H,J){for(var X in H)if(Object.prototype.hasOwnProperty.call(H,X)&&J(H[X]))return X;return}function c(H,J){for(var X=0;X<H.length;X++)if(J(H[X]))return X;return}var d=/^(\d+)(th|st|nd|rd)?/i,p=/\d+/i,g={narrow:/^(ق|ب)/i,abbreviated:/^(ق\.?\s?م\.?|ق\.?\s?م\.?\s?|a\.?\s?d\.?|c\.?\s?)/i,wide:/^(قبل الميلاد|قبل الميلاد|بعد الميلاد|بعد الميلاد)/i},l={any:[/^قبل/i,/^بعد/i]},u={narrow:/^[1234]/i,abbreviated:/^ر[1234]/i,wide:/^الربع [1234]/i},i={any:[/1/i,/2/i,/3/i,/4/i]},n={narrow:/^[جفمأسند]/i,abbreviated:/^(جان|فيف|مار|أفر|ماي|جوا|جوي|أوت|سبت|أكت|نوف|ديس)/i,wide:/^(جانفي|فيفري|مارس|أفريل|ماي|جوان|جويلية|أوت|سبتمبر|أكتوبر|نوفمبر|ديسمبر)/i},s={narrow:[/^ج/i,/^ف/i,/^م/i,/^أ/i,/^م/i,/^ج/i,/^ج/i,/^أ/i,/^س/i,/^أ/i,/^ن/i,/^د/i],any:[/^جان/i,/^فيف/i,/^مار/i,/^أفر/i,/^ماي/i,/^جوا/i,/^جوي/i,/^أوت/i,/^سبت/i,/^أكت/i,/^نوف/i,/^ديس/i]},o={narrow:/^[حنثرخجس]/i,short:/^(أحد|اثنين|ثلاثاء|أربعاء|خميس|جمعة|سبت)/i,abbreviated:/^(أحد|اثن|ثلا|أرب|خمي|جمعة|سبت)/i,wide:/^(الأحد|الاثنين|الثلاثاء|الأربعاء|الخميس|الجمعة|السبت)/i},r={narrow:[/^ح/i,/^ن/i,/^ث/i,/^ر/i,/^خ/i,/^ج/i,/^س/i],wide:[/^الأحد/i,/^الاثنين/i,/^الثلاثاء/i,/^الأربعاء/i,/^الخميس/i,/^الجمعة/i,/^السبت/i],any:[/^أح/i,/^اث/i,/^ث/i,/^أر/i,/^خ/i,/^ج/i,/^س/i]},e={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},a={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},t={ordinalNumber:m({matchPattern:d,parsePattern:p,valueCallback:function H(J){return parseInt(J,10)}}),era:q({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:l,defaultParseWidth:"any"}),quarter:q({matchPatterns:u,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any",valueCallback:function H(J){return Number(J)+1}}),month:q({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),day:q({matchPatterns:o,defaultMatchWidth:"wide",parsePatterns:r,defaultParseWidth:"any"}),dayPeriod:q({matchPatterns:e,defaultMatchWidth:"any",parsePatterns:a,defaultParseWidth:"any"})},HH={code:"ar-DZ",formatDistance:V,formatLong:L,formatRelative:w,localize:h,match:t,options:{weekStartsOn:0,firstWeekContainsDate:1}};window.dateFns=A(A({},window.dateFns),{},{locale:A(A({},($=window.dateFns)===null||$===void 0?void 0:$.locale),{},{arDZ:HH})})})();

//# debugId=22144BF3B911ADFE64756E2164756E21
