(()=>{var $;function B(G){return B=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(H){return typeof H}:function(H){return H&&typeof Symbol=="function"&&H.constructor===Symbol&&H!==Symbol.prototype?"symbol":typeof H},B(G)}function A(G,H){var J=Object.keys(G);if(Object.getOwnPropertySymbols){var X=Object.getOwnPropertySymbols(G);H&&(X=X.filter(function(Y){return Object.getOwnPropertyDescriptor(G,Y).enumerable})),J.push.apply(J,X)}return J}function Q(G){for(var H=1;H<arguments.length;H++){var J=arguments[H]!=null?arguments[H]:{};H%2?A(Object(J),!0).forEach(function(X){E(G,X,J[X])}):Object.getOwnPropertyDescriptors?Object.defineProperties(G,Object.getOwnPropertyDescriptors(J)):A(Object(J)).forEach(function(X){Object.defineProperty(G,X,Object.getOwnPropertyDescriptor(J,X))})}return G}function E(G,H,J){if(H=N(H),H in G)Object.defineProperty(G,H,{value:J,enumerable:!0,configurable:!0,writable:!0});else G[H]=J;return G}function N(G){var H=z(G,"string");return B(H)=="symbol"?H:String(H)}function z(G,H){if(B(G)!="object"||!G)return G;var J=G[Symbol.toPrimitive];if(J!==void 0){var X=J.call(G,H||"default");if(B(X)!="object")return X;throw new TypeError("@@toPrimitive must return a primitive value.")}return(H==="string"?String:Number)(G)}var W=Object.defineProperty,JG=function G(H,J){for(var X in J)W(H,X,{get:J[X],enumerable:!0,configurable:!0,set:function Y(Z){return J[X]=function(){return Z}}})},D={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},S=function G(H,J,X,Y){return D[H]};function I(G){return function(H,J){var X=J!==null&&J!==void 0&&J.context?String(J.context):"standalone",Y;if(X==="formatting"&&G.formattingValues){var Z=G.defaultFormattingWidth||G.defaultWidth,C=J!==null&&J!==void 0&&J.width?String(J.width):Z;Y=G.formattingValues[C]||G.formattingValues[Z]}else{var T=G.defaultWidth,q=J!==null&&J!==void 0&&J.width?String(J.width):G.defaultWidth;Y=G.values[q]||G.values[T]}var U=G.argumentCallback?G.argumentCallback(H):H;return Y[U]}}var M={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},R={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},L={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},V={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},j={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},w={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},_=function G(H,J){var X=Number(H),Y=X%100;if(Y>20||Y<10)switch(Y%10){case 1:return X+"st";case 2:return X+"nd";case 3:return X+"rd"}return X+"th"},f={ordinalNumber:_,era:I({values:M,defaultWidth:"wide"}),quarter:I({values:R,defaultWidth:"wide",argumentCallback:function G(H){return H-1}}),month:I({values:L,defaultWidth:"wide"}),day:I({values:V,defaultWidth:"wide"}),dayPeriod:I({values:j,defaultWidth:"wide",formattingValues:w,defaultFormattingWidth:"wide"})};function O(G){return function(H){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=J.width,Y=X&&G.matchPatterns[X]||G.matchPatterns[G.defaultMatchWidth],Z=H.match(Y);if(!Z)return null;var C=Z[0],T=X&&G.parsePatterns[X]||G.parsePatterns[G.defaultParseWidth],q=Array.isArray(T)?F(T,function(x){return x.test(C)}):v(T,function(x){return x.test(C)}),U;U=G.valueCallback?G.valueCallback(q):q,U=J.valueCallback?J.valueCallback(U):U;var HG=H.slice(C.length);return{value:U,rest:HG}}}function v(G,H){for(var J in G)if(Object.prototype.hasOwnProperty.call(G,J)&&H(G[J]))return J;return}function F(G,H){for(var J=0;J<G.length;J++)if(H(G[J]))return J;return}function P(G){return function(H){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=H.match(G.matchPattern);if(!X)return null;var Y=X[0],Z=H.match(G.parsePattern);if(!Z)return null;var C=G.valueCallback?G.valueCallback(Z[0]):Z[0];C=J.valueCallback?J.valueCallback(C):C;var T=H.slice(Y.length);return{value:C,rest:T}}}var k=/^(\d+)(th|st|nd|rd)?/i,h=/\d+/i,b={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},c={any:[/^b/i,/^(a|c)/i]},m={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},y={any:[/1/i,/2/i,/3/i,/4/i]},p={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},d={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},g={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},u={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},l={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},i={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},n={ordinalNumber:P({matchPattern:k,parsePattern:h,valueCallback:function G(H){return parseInt(H,10)}}),era:O({matchPatterns:b,defaultMatchWidth:"wide",parsePatterns:c,defaultParseWidth:"any"}),quarter:O({matchPatterns:m,defaultMatchWidth:"wide",parsePatterns:y,defaultParseWidth:"any",valueCallback:function G(H){return H+1}}),month:O({matchPatterns:p,defaultMatchWidth:"wide",parsePatterns:d,defaultParseWidth:"any"}),day:O({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"any"}),dayPeriod:O({matchPatterns:l,defaultMatchWidth:"any",parsePatterns:i,defaultParseWidth:"any"})},s={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"a second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"a minute",other:"{{count}} minutes"},aboutXHours:{one:"about an hour",other:"about {{count}} hours"},xHours:{one:"an hour",other:"{{count}} hours"},xDays:{one:"a day",other:"{{count}} days"},aboutXWeeks:{one:"about a week",other:"about {{count}} weeks"},xWeeks:{one:"a week",other:"{{count}} weeks"},aboutXMonths:{one:"about a month",other:"about {{count}} months"},xMonths:{one:"a month",other:"{{count}} months"},aboutXYears:{one:"about a year",other:"about {{count}} years"},xYears:{one:"a year",other:"{{count}} years"},overXYears:{one:"over a year",other:"over {{count}} years"},almostXYears:{one:"almost a year",other:"almost {{count}} years"}},o=function G(H,J,X){var Y,Z=s[H];if(typeof Z==="string")Y=Z;else if(J===1)Y=Z.one;else Y=Z.other.replace("{{count}}",J.toString());if(X!==null&&X!==void 0&&X.addSuffix)if(X.comparison&&X.comparison>0)return"in "+Y;else return Y+" ago";return Y};function K(G){return function(){var H=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},J=H.width?String(H.width):G.defaultWidth,X=G.formats[J]||G.formats[G.defaultWidth];return X}}var r={full:"EEEE, MMMM do, yyyy",long:"MMMM do, yyyy",medium:"MMM d, yyyy",short:"yyyy-MM-dd"},a={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},e={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},t={date:K({formats:r,defaultWidth:"full"}),time:K({formats:a,defaultWidth:"full"}),dateTime:K({formats:e,defaultWidth:"full"})},GG={code:"en-CA",formatDistance:o,formatLong:t,formatRelative:S,localize:f,match:n,options:{weekStartsOn:0,firstWeekContainsDate:1}};window.dateFns=Q(Q({},window.dateFns),{},{locale:Q(Q({},($=window.dateFns)===null||$===void 0?void 0:$.locale),{},{enCA:GG})})})();

//# debugId=84B04FC62D904C5464756E2164756E21
